import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';
import { envelopeEncrypt, envelopeDecrypt } from '@/lib/crypto/envelope';
import { rateLimit } from '@/lib/utils/rate-limit';
import {
  validateApiKeyRequest,
  createSecureErrorResponse,
  getSecurityHeaders,
  generateRateLimitKey,
  createAuditLog,
  logAuditEntry,
  getLastNChars,
  validateEnvironmentVariables,
} from '@/lib/utils/security';

// Validate environment variables on startup
validateEnvironmentVariables();

// Initialize Supabase client with service role key for server operations
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
const masterKey = process.env.MASTER_KEY!;

const supabaseServer = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

// Rate limiting configuration
const rateLimiter = rateLimit({
  interval: 60 * 1000, // 1 minute
  uniqueTokenPerInterval: 500, // Max 500 unique users per minute
});

// Enhanced rate limiting for sensitive operations
const sensitiveRateLimiter = rateLimit({
  interval: 60 * 1000, // 1 minute
  uniqueTokenPerInterval: 100, // Max 100 unique users per minute for sensitive ops
});

// Types
interface ApiKeyRequest {
  provider: 'alpaca';
  apiKey: string;
  apiSecret: string;
}

interface ApiKeyMetadata {
  provider: string;
  api_key_last4: string;
  created_at: string;
  updated_at: string;
}

// Helper function to get user from JWT token with enhanced security
async function getUserFromToken(request: NextRequest): Promise<{ userId: string | null; error?: string }> {
  try {
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.startsWith('Bearer ')) {
      return { userId: null, error: 'Missing or invalid authorization header' };
    }

    const token = authHeader.substring(7);
    if (!token || token.length < 10) {
      return { userId: null, error: 'Invalid token format' };
    }

    const { data: { user }, error } = await supabaseServer.auth.getUser(token);

    if (error) {
      return { userId: null, error: 'Token validation failed' };
    }

    if (!user?.id) {
      return { userId: null, error: 'Invalid user' };
    }

    return { userId: user.id };
  } catch (error) {
    return { userId: null, error: 'Authentication error' };
  }
}

// Helper function to create secure response with headers
function createSecureResponse(data: any, status: number = 200): NextResponse {
  const response = NextResponse.json(data, { status });

  // Add security headers
  const securityHeaders = getSecurityHeaders();
  Object.entries(securityHeaders).forEach(([key, value]) => {
    response.headers.set(key, value);
  });

  return response;
}

// POST - Save or rotate API key
export async function POST(request: NextRequest) {
  const startTime = Date.now();
  let userId: string | null = null;
  let auditAction = 'api_key_save_attempt';

  try {
    // Enhanced rate limiting for sensitive operations
    const clientIp = request.ip ?? request.headers.get('x-forwarded-for') ?? 'anonymous';
    const rateLimitKey = generateRateLimitKey(clientIp, '/api/user/api-keys', 'POST');
    const { success } = await sensitiveRateLimiter.check(3, rateLimitKey); // 3 requests per minute

    if (!success) {
      const errorResponse = createSecureErrorResponse(
        'Too many requests. Please try again later.',
        'RATE_LIMIT_EXCEEDED',
        `Rate limit exceeded for IP: ${clientIp}`
      );

      logAuditEntry(createAuditLog(auditAction, false, {
        ip: clientIp,
        error: 'Rate limit exceeded',
      }));

      return createSecureResponse(errorResponse, 429);
    }

    // Authenticate user with enhanced error handling
    const authResult = await getUserFromToken(request);
    if (!authResult.userId) {
      const errorResponse = createSecureErrorResponse(
        'Unauthorized',
        'AUTH_FAILED',
        `Authentication failed: ${authResult.error}`
      );

      logAuditEntry(createAuditLog(auditAction, false, {
        ip: clientIp,
        error: authResult.error || 'Authentication failed',
      }));

      return createSecureResponse(errorResponse, 401);
    }

    userId = authResult.userId;

    // Parse and validate request body with enhanced security
    let body: ApiKeyRequest;
    try {
      const rawBody = await request.text();
      if (!rawBody || rawBody.length > 10000) { // Limit body size
        throw new Error('Invalid request body size');
      }
      body = JSON.parse(rawBody);
    } catch (error) {
      const errorResponse = createSecureErrorResponse(
        'Invalid request body',
        'INVALID_JSON',
        `JSON parse error: ${error instanceof Error ? error.message : 'Unknown'}`
      );

      logAuditEntry(createAuditLog(auditAction, false, {
        userId,
        ip: clientIp,
        error: 'Invalid JSON in request body',
      }));

      return createSecureResponse(errorResponse, 400);
    }

    // Enhanced input validation
    const validation = validateApiKeyRequest(body);
    if (!validation.isValid) {
      const errorResponse = createSecureErrorResponse(
        validation.errors[0], // Return first error only
        'VALIDATION_FAILED',
        `Validation errors: ${validation.errors.join(', ')}`
      );

      logAuditEntry(createAuditLog(auditAction, false, {
        userId,
        provider: body?.provider,
        ip: clientIp,
        error: `Validation failed: ${validation.errors.join(', ')}`,
      }));

      return createSecureResponse(errorResponse, 400);
    }

    const { provider, apiKey, apiSecret } = body;

    // Encrypt the API secret using envelope encryption
    const encryptionResult = await envelopeEncrypt(apiSecret, masterKey);

    // Get last 4 characters of API key for display (secure)
    const apiKeyLast4 = getLastNChars(apiKey, 4);

    // Store in database (upsert to handle key rotation)
    const { data, error } = await supabaseServer
      .from('user_api_secrets')
      .upsert({
        user_id: userId,
        provider,
        encrypted_key: encryptionResult.encryptedData,
        iv: encryptionResult.iv,
        tag: encryptionResult.tag,
        encrypted_dek: encryptionResult.encryptedDek,
        api_key_last4: apiKeyLast4,
        updated_at: new Date().toISOString(),
      }, {
        onConflict: 'user_id,provider'
      })
      .select('provider, api_key_last4, created_at, updated_at')
      .single();

    if (error) {
      const errorResponse = createSecureErrorResponse(
        'Failed to save API key',
        'DATABASE_ERROR',
        `Database error: ${error.message || 'Unknown database error'}`
      );

      logAuditEntry(createAuditLog(auditAction, false, {
        userId,
        provider,
        ip: clientIp,
        error: `Database error: ${error.code || error.message}`,
      }));

      return createSecureResponse(errorResponse, 500);
    }

    // Success - log audit entry
    auditAction = 'api_key_saved';
    logAuditEntry(createAuditLog(auditAction, true, {
      userId,
      provider,
      ip: clientIp,
    }));

    // Return safe metadata only
    const responseData = {
      provider: data.provider,
      last4: data.api_key_last4,
      created_at: data.created_at,
      updated_at: data.updated_at,
    };

    return createSecureResponse(responseData);

  } catch (error) {
    const errorResponse = createSecureErrorResponse(
      'Internal server error',
      'INTERNAL_ERROR',
      `Unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}`
    );

    logAuditEntry(createAuditLog(auditAction, false, {
      userId,
      ip: clientIp,
      error: error instanceof Error ? error.message : 'Unknown error',
    }));

    return createSecureResponse(errorResponse, 500);
  }
}

// GET - Fetch masked API key metadata
export async function GET(request: NextRequest) {
  let userId: string | null = null;
  const auditAction = 'api_key_fetch_attempt';

  try {
    const clientIp = request.ip ?? request.headers.get('x-forwarded-for') ?? 'anonymous';

    // Authenticate user with enhanced error handling
    const authResult = await getUserFromToken(request);
    if (!authResult.userId) {
      const errorResponse = createSecureErrorResponse(
        'Unauthorized',
        'AUTH_FAILED',
        `Authentication failed: ${authResult.error}`
      );

      logAuditEntry(createAuditLog(auditAction, false, {
        ip: clientIp,
        error: authResult.error || 'Authentication failed',
      }));

      return createSecureResponse(errorResponse, 401);
    }

    userId = authResult.userId;

    // Get provider from query params with validation
    const { searchParams } = new URL(request.url);
    const provider = searchParams.get('provider') || 'alpaca';

    if (provider !== 'alpaca') {
      const errorResponse = createSecureErrorResponse(
        'Unsupported provider',
        'INVALID_PROVIDER',
        `Unsupported provider: ${provider}`
      );

      logAuditEntry(createAuditLog(auditAction, false, {
        userId,
        provider,
        ip: clientIp,
        error: 'Unsupported provider',
      }));

      return createSecureResponse(errorResponse, 400);
    }

    // Fetch metadata (no encrypted data) - explicitly select only safe fields
    const { data, error } = await supabaseServer
      .from('user_api_secrets')
      .select('provider, api_key_last4, created_at, updated_at')
      .eq('user_id', userId)
      .eq('provider', provider)
      .single();

    if (error) {
      if (error.code === 'PGRST116') { // No rows returned
        logAuditEntry(createAuditLog('api_key_not_found', true, {
          userId,
          provider,
          ip: clientIp,
        }));
        return createSecureResponse(null);
      }

      const errorResponse = createSecureErrorResponse(
        'Failed to fetch API key metadata',
        'DATABASE_ERROR',
        `Database error: ${error.message || 'Unknown database error'}`
      );

      logAuditEntry(createAuditLog(auditAction, false, {
        userId,
        provider,
        ip: clientIp,
        error: `Database error: ${error.code || error.message}`,
      }));

      return createSecureResponse(errorResponse, 500);
    }

    // Success - log audit entry
    logAuditEntry(createAuditLog('api_key_fetched', true, {
      userId,
      provider,
      ip: clientIp,
    }));

    // Return only safe metadata
    const responseData = {
      provider: data.provider,
      last4: data.api_key_last4,
      created_at: data.created_at,
      updated_at: data.updated_at,
    };

    return createSecureResponse(responseData);

  } catch (error) {
    const errorResponse = createSecureErrorResponse(
      'Internal server error',
      'INTERNAL_ERROR',
      `Unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}`
    );

    logAuditEntry(createAuditLog(auditAction, false, {
      userId,
      ip: clientIp,
      error: error instanceof Error ? error.message : 'Unknown error',
    }));

    return createSecureResponse(errorResponse, 500);
  }
}

// DELETE - Remove API key
export async function DELETE(request: NextRequest) {
  let userId: string | null = null;
  const auditAction = 'api_key_delete_attempt';

  try {
    const clientIp = request.ip ?? request.headers.get('x-forwarded-for') ?? 'anonymous';

    // Enhanced rate limiting for delete operations
    const rateLimitKey = generateRateLimitKey(clientIp, '/api/user/api-keys', 'DELETE');
    const { success } = await sensitiveRateLimiter.check(5, rateLimitKey); // 5 deletes per minute

    if (!success) {
      const errorResponse = createSecureErrorResponse(
        'Too many requests. Please try again later.',
        'RATE_LIMIT_EXCEEDED',
        `Delete rate limit exceeded for IP: ${clientIp}`
      );

      logAuditEntry(createAuditLog(auditAction, false, {
        ip: clientIp,
        error: 'Rate limit exceeded',
      }));

      return createSecureResponse(errorResponse, 429);
    }

    // Authenticate user with enhanced error handling
    const authResult = await getUserFromToken(request);
    if (!authResult.userId) {
      const errorResponse = createSecureErrorResponse(
        'Unauthorized',
        'AUTH_FAILED',
        `Authentication failed: ${authResult.error}`
      );

      logAuditEntry(createAuditLog(auditAction, false, {
        ip: clientIp,
        error: authResult.error || 'Authentication failed',
      }));

      return createSecureResponse(errorResponse, 401);
    }

    userId = authResult.userId;

    // Get provider from query params with validation
    const { searchParams } = new URL(request.url);
    const provider = searchParams.get('provider') || 'alpaca';

    if (provider !== 'alpaca') {
      const errorResponse = createSecureErrorResponse(
        'Unsupported provider',
        'INVALID_PROVIDER',
        `Unsupported provider: ${provider}`
      );

      logAuditEntry(createAuditLog(auditAction, false, {
        userId,
        provider,
        ip: clientIp,
        error: 'Unsupported provider',
      }));

      return createSecureResponse(errorResponse, 400);
    }

    // Delete the API key
    const { error } = await supabaseServer
      .from('user_api_secrets')
      .delete()
      .eq('user_id', userId)
      .eq('provider', provider);

    if (error) {
      const errorResponse = createSecureErrorResponse(
        'Failed to delete API key',
        'DATABASE_ERROR',
        `Database error: ${error.message || 'Unknown database error'}`
      );

      logAuditEntry(createAuditLog(auditAction, false, {
        userId,
        provider,
        ip: clientIp,
        error: `Database error: ${error.code || error.message}`,
      }));

      return createSecureResponse(errorResponse, 500);
    }

    // Success - log audit entry
    logAuditEntry(createAuditLog('api_key_deleted', true, {
      userId,
      provider,
      ip: clientIp,
    }));

    return createSecureResponse({ success: true });

  } catch (error) {
    const errorResponse = createSecureErrorResponse(
      'Internal server error',
      'INTERNAL_ERROR',
      `Unexpected error: ${error instanceof Error ? error.message : 'Unknown error'}`
    );

    logAuditEntry(createAuditLog(auditAction, false, {
      userId,
      ip: clientIp,
      error: error instanceof Error ? error.message : 'Unknown error',
    }));

    return createSecureResponse(errorResponse, 500);
  }
}
