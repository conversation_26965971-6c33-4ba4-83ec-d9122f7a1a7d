# Trade Locker

A Next.js trading application with Supabase authentication.

## Authentication Setup

This app uses Supabase for email/password authentication. Follow these steps to set up authentication:

### 1. Create a Supabase Project

1. Go to [https://supabase.com](https://supabase.com) and create a new account or sign in
2. Create a new project
3. Wait for the project to be set up (this may take a few minutes)

### 2. Get Your Supabase Credentials

1. In your Supabase project dashboard, go to **Settings** > **API**
2. Copy the following values:
   - **Project URL** (under "Project URL")
   - **anon public** key (under "Project API keys")

### 3. Configure Environment Variables

1. Copy the example environment file:
   ```bash
   cp .env.local.example .env.local
   ```

2. Edit `.env.local` and replace the placeholder values with your actual credentials:
   ```env
   # Supabase Configuration
   NEXT_PUBLIC_SUPABASE_URL=your_actual_supabase_project_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_actual_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_actual_supabase_service_role_key

   # Encryption Configuration
   MASTER_KEY=your_base64_encoded_master_key_here
   ```

3. **Get your Supabase Service Role Key:**
   - In your Supabase project dashboard, go to **Settings** > **API**
   - Copy the **service_role** key (under "Project API keys")
   - **Important:** Keep this key secure and never expose it in client-side code

4. **Generate a Master Key for encryption:**
   ```bash
   node -e "console.log(require('crypto').randomBytes(32).toString('base64'))"
   ```
   - Copy the generated key and use it as your `MASTER_KEY`
   - **Important:** This key encrypts all API secrets. Keep it secure and backed up!

### 4. Configure Supabase Authentication

1. In your Supabase project dashboard, go to **Authentication** > **Settings**
2. Under **Site URL**, add your local development URL: `http://localhost:3000`
3. Under **Redirect URLs**, add: `http://localhost:3000/**`
4. Make sure **Enable email confirmations** is enabled if you want users to verify their email addresses

### 5. Authentication Features

The app includes:
- **Sign Up**: Create new accounts with email/password
- **Sign In**: Login with existing credentials
- **Sign Out**: Logout functionality in the header
- **Password Reset**: Send password reset emails
- **Session Persistence**: Users stay logged in across browser refreshes
- **Protected Routes**: The main app content is only accessible when authenticated

### 6. How It Works

- When users visit the app without being authenticated, they see a full-screen authentication overlay
- The overlay has three tabs: Login, Create Account, and Forgot Password
- After successful authentication, the overlay disappears and the main app content becomes accessible
- Users can sign out using the "Sign Out" button in the header
- Sessions persist across browser refreshes

## Secure API Key Storage

The app includes a secure API key storage system for trading APIs (currently supports Alpaca):

### Security Features
- **Envelope Encryption**: API secrets are encrypted using AES-256-GCM with envelope encryption
- **Zero-Trust Architecture**: API keys are never stored in plaintext, never logged, and never sent to the client
- **Per-Record Encryption**: Each API key gets its own Data Encryption Key (DEK)
- **Master Key Protection**: DEKs are encrypted with a master key stored in environment variables
- **Rate Limited**: API key operations are rate-limited to prevent abuse
- **Row-Level Security**: Database access is restricted using Supabase RLS policies

### How It Works
1. User submits API key and secret through the Settings modal
2. Server generates a random DEK for this specific record
3. API secret is encrypted with the DEK using AES-256-GCM
4. DEK is encrypted with the master key (envelope encryption)
5. Only encrypted data, IV, auth tag, and encrypted DEK are stored
6. API key last 4 characters are stored for display purposes
7. Users can view masked metadata, replace, or delete their keys

### Database Security
- Users can only read metadata of their own API keys (no encrypted data)
- Direct inserts/updates are blocked by RLS policies
- Only server routes with service role can write to the database
- All operations require valid JWT authentication

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [Geist](https://vercel.com/font), a new font family for Vercel.

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
