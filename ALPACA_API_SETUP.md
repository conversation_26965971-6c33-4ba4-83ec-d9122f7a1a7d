# Alpaca API Key Storage Setup Guide

This guide explains how to set up and use the secure Alpaca API key storage feature in your Next.js + Supabase application.

## 🔐 Security Features

- **Envelope Encryption**: API secrets are encrypted using AES-256-GCM with per-record Data Encryption Keys (DEK)
- **Master Key Protection**: DEKs are encrypted with a master key stored in environment variables
- **Zero-Trust Architecture**: API keys are never stored in plaintext, never logged, and never sent to the client
- **Row-Level Security**: Database access is restricted using Supabase RLS policies
- **Rate Limiting**: API operations are rate-limited to prevent abuse
- **Audit Logging**: All operations are logged for security monitoring

## 📋 Prerequisites

1. Next.js application with Supabase authentication
2. Supabase project with service role access
3. Node.js environment with WebCrypto support

## 🚀 Setup Instructions

### 1. Environment Variables

Add the following to your `.env.local` file:

```env
# Existing Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# Server-side Supabase Configuration
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Encryption Configuration
MASTER_KEY=your_base64_encoded_master_key_here
```

### 2. Generate Master Key

Generate a secure 256-bit master key:

```bash
node -e "console.log(require('crypto').randomBytes(32).toString('base64'))"
```

Copy the output and use it as your `MASTER_KEY` environment variable.

**⚠️ Important**: Keep this key secure and backed up! If you lose it, all encrypted API secrets will be unrecoverable.

### 3. Run Database Migration

Apply the database migration to create the `user_api_secrets` table:

```sql
-- Run this in your Supabase SQL editor or via CLI
-- File: supabase/migrations/001_create_user_api_secrets.sql
```

The migration will:
- Create the `user_api_secrets` table with proper encryption columns
- Set up Row-Level Security (RLS) policies
- Create indexes for efficient lookups
- Grant necessary permissions to the service role

## 🧪 Testing

### Manual Testing

1. **Save API Key**:
   ```bash
   curl -X POST http://localhost:3000/api/user/api-keys \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -d '{
       "provider": "alpaca",
       "apiKey": "ABCDEFGHIJ1234567890",
       "apiSecret": "abcdefghijklmnopqrstuvwxyz1234567890ABCD"
     }'
   ```

2. **Fetch Masked Metadata**:
   ```bash
   curl -X GET "http://localhost:3000/api/user/api-keys?provider=alpaca" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN"
   ```

3. **Delete API Key**:
   ```bash
   curl -X DELETE "http://localhost:3000/api/user/api-keys?provider=alpaca" \
     -H "Authorization: Bearer YOUR_JWT_TOKEN"
   ```

## 🎯 Usage

### In the UI

1. Open the User Settings modal (gear icon in the chart toolbar)
2. Scroll down to the "Alpaca API Keys" section
3. Click "Add Alpaca API Key" to add your first key
4. Enter your Alpaca API Key and Secret
5. Click "Save Key"

The UI will show:
- Masked API key display: `**** **** **** 7890`
- Creation/update timestamps
- Replace and Delete buttons for key management

### Programmatic Access

To use the stored API keys in your application:

```typescript
// This would be implemented in a server-side function
import { envelopeDecrypt } from '@/lib/crypto/envelope';
import { supabaseServer } from '@/lib/supabase/serverClient';

async function getDecryptedApiSecret(userId: string, provider: string): Promise<string | null> {
  // Fetch encrypted data (server-side only)
  const { data, error } = await supabaseServer
    .from('user_api_secrets')
    .select('encrypted_key, iv, tag, encrypted_dek')
    .eq('user_id', userId)
    .eq('provider', provider)
    .single();

  if (error || !data) {
    return null;
  }

  // Decrypt the API secret
  const masterKey = process.env.MASTER_KEY!;
  const decrypted = await envelopeDecrypt({
    encryptedData: data.encrypted_key,
    iv: data.iv,
    tag: data.tag,
    encryptedDek: data.encrypted_dek,
  }, masterKey);

  return decrypted;
}
```

## 🔒 Security Considerations

### What's Protected

✅ API secrets are encrypted with AES-256-GCM  
✅ Each record uses a unique Data Encryption Key  
✅ DEKs are encrypted with a master key  
✅ No plaintext secrets in database or logs  
✅ Rate limiting prevents brute force attacks  
✅ Input validation prevents injection attacks  
✅ Audit logging tracks all operations  
✅ RLS policies restrict database access  

### Best Practices

1. **Master Key Management**:
   - Store master key in secure environment variables
   - Never commit master key to version control
   - Rotate master key periodically (requires re-encryption)
   - Back up master key securely

2. **Access Control**:
   - Only server-side code can decrypt API secrets
   - Client-side code only sees masked metadata
   - Use proper JWT validation for all requests

3. **Monitoring**:
   - Monitor audit logs for suspicious activity
   - Set up alerts for failed authentication attempts
   - Track rate limit violations

4. **Development vs Production**:
   - Use different master keys for each environment
   - Never use production keys in development
   - Test encryption/decryption in staging environment

## 🚨 Troubleshooting

### Common Issues

1. **"Missing required environment variables"**:
   - Ensure all environment variables are set
   - Check that MASTER_KEY is a valid base64-encoded 32-byte key

2. **"Authentication failed"**:
   - Verify JWT token is valid and not expired
   - Check Supabase service role key is correct

3. **"Rate limit exceeded"**:
   - Wait before retrying the request
   - Check if multiple users are sharing the same IP

4. **"Decryption failed"**:
   - Verify master key hasn't changed
   - Check that encrypted data hasn't been corrupted

### Debug Mode

Enable debug logging in development:

```env
NODE_ENV=development
```

This will log audit entries to the console for debugging.

## 📚 API Reference

### POST /api/user/api-keys
Save or rotate an API key.

**Request Body**:
```json
{
  "provider": "alpaca",
  "apiKey": "ABCDEFGHIJ1234567890",
  "apiSecret": "abcdefghijklmnopqrstuvwxyz1234567890ABCD"
}
```

**Response**:
```json
{
  "provider": "alpaca",
  "last4": "7890",
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

### GET /api/user/api-keys?provider=alpaca
Fetch masked API key metadata.

**Response**:
```json
{
  "provider": "alpaca",
  "last4": "7890",
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

### DELETE /api/user/api-keys?provider=alpaca
Delete an API key.

**Response**:
```json
{
  "success": true
}
```

## 🔄 Migration and Updates

When updating the system:

1. **Database Schema Changes**: Create new migration files
2. **Encryption Changes**: Plan for data re-encryption if needed
3. **API Changes**: Maintain backward compatibility
4. **Security Updates**: Test thoroughly in staging environment

For questions or issues, refer to the API documentation above for examples of proper usage.
