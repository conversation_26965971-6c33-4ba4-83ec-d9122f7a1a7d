'use client';

import { useEffect, useState } from 'react';
import Header from '../components/Header';
import ChartSection from '../components/ChartSection';
import StockOrderForm from '../components/StockOrderForm';
import AuthOverlay from '../components/AuthOverlay';
import { useAuth } from '../components/AuthProvider';

export default function Home() {
  const { user, loading } = useAuth();
  const [selectedTool, setSelectedTool] = useState('crosshair');
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [isTradingRulesExpanded, setIsTradingRulesExpanded] = useState(false);

  // Initialize dark mode from localStorage
  useEffect(() => {
    const savedTheme = localStorage.getItem('darkMode');
    if (savedTheme !== null) {
      setIsDarkMode(JSON.parse(savedTheme));
    }
  }, []);

  // Apply dark mode to document
  useEffect(() => {
    if (isDarkMode) {
      document.documentElement.classList.add('dark');
      document.body.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
      document.body.classList.remove('dark');
    }
    console.log('Dark mode applied:', isDarkMode, document.documentElement.classList.contains('dark'));
  }, [isDarkMode]);

  // Toggle dark mode and save to localStorage
  const toggleDarkMode = () => {
    const newDarkMode = !isDarkMode;
    setIsDarkMode(newDarkMode);
    localStorage.setItem('darkMode', JSON.stringify(newDarkMode));
  };

  // Show loading state while checking authentication
  if (loading) {
    return (
      <div className="h-screen flex items-center justify-center bg-white dark:bg-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading...</p>
        </div>
      </div>
    );
  }

  // Show auth overlay if user is not authenticated
  if (!user) {
    return <AuthOverlay />;
  }

  // Show main app content if user is authenticated
  return (
    <div className="h-screen flex flex-col bg-white dark:bg-gray-900">
      <Header isDarkMode={isDarkMode} onToggleDarkMode={toggleDarkMode} />

      {/* Main Content */}
      <div className="flex-1 flex bg-white dark:bg-gray-900">
        <ChartSection
          selectedTool={selectedTool}
          onToolSelect={setSelectedTool}
          isDarkMode={isDarkMode}
          isTradingRulesExpanded={isTradingRulesExpanded}
          onToggleTradingRules={() => setIsTradingRulesExpanded(!isTradingRulesExpanded)}
        />

        {/* Order Form Section - 1/5 of the page */}
        <div className={`w-1/5 p-4 border-l ${isDarkMode ? 'border-gray-700 bg-gray-900' : 'border-gray-200 bg-white'}`}>
          <StockOrderForm isDarkMode={isDarkMode} />
        </div>
      </div>
    </div>
  );
}
