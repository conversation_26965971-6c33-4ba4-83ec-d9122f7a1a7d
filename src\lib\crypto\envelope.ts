/**
 * Envelope Encryption Module for Secure API Key Storage
 * 
 * This module implements envelope encryption using AES-256-GCM:
 * 1. Generate a random Data Encryption Key (DEK) for each record
 * 2. Encrypt the sensitive data with the DEK
 * 3. Encrypt the DEK with a Master Key (KEK - Key Encryption Key)
 * 4. Store the encrypted data, encrypted DEK, IV, and auth tag
 */

// Types for encryption operations
export interface EncryptionResult {
  encryptedData: string; // Base64 encoded encrypted data
  iv: string; // Base64 encoded initialization vector
  tag: string; // Base64 encoded authentication tag
}

export interface EnvelopeEncryptionResult extends EncryptionResult {
  encryptedDek: string; // Base64 encoded encrypted DEK
}

export interface DecryptionInput {
  encryptedData: string;
  iv: string;
  tag: string;
}

export interface EnvelopeDecryptionInput extends DecryptionInput {
  encryptedDek: string;
}

// Error classes for better error handling
export class CryptoError extends Error {
  constructor(message: string, public readonly operation: string) {
    super(message);
    this.name = 'CryptoError';
  }
}

/**
 * Generate a random 256-bit Data Encryption Key (DEK)
 */
export async function generateDek(): Promise<CryptoKey> {
  try {
    return await crypto.subtle.generateKey(
      {
        name: 'AES-GCM',
        length: 256,
      },
      true, // extractable
      ['encrypt', 'decrypt']
    );
  } catch (error) {
    throw new CryptoError(
      `Failed to generate DEK: ${error instanceof Error ? error.message : 'Unknown error'}`,
      'generateDek'
    );
  }
}

/**
 * Encrypt data using a DEK with AES-256-GCM
 */
export async function encryptWithDek(
  data: string,
  dek: CryptoKey
): Promise<EncryptionResult> {
  try {
    // Generate a random 96-bit IV (12 bytes) for AES-GCM
    const iv = crypto.getRandomValues(new Uint8Array(12));
    
    // Convert string to bytes
    const dataBytes = new TextEncoder().encode(data);
    
    // Encrypt the data
    const encryptedBuffer = await crypto.subtle.encrypt(
      {
        name: 'AES-GCM',
        iv: iv,
        tagLength: 128, // 128-bit authentication tag
      },
      dek,
      dataBytes
    );
    
    // Extract the encrypted data and authentication tag
    const encryptedArray = new Uint8Array(encryptedBuffer);
    const encryptedData = encryptedArray.slice(0, -16); // All but last 16 bytes
    const tag = encryptedArray.slice(-16); // Last 16 bytes
    
    return {
      encryptedData: arrayBufferToBase64(encryptedData),
      iv: arrayBufferToBase64(iv),
      tag: arrayBufferToBase64(tag),
    };
  } catch (error) {
    throw new CryptoError(
      `Failed to encrypt with DEK: ${error instanceof Error ? error.message : 'Unknown error'}`,
      'encryptWithDek'
    );
  }
}

/**
 * Decrypt data using a DEK with AES-256-GCM
 */
export async function decryptWithDek(
  input: DecryptionInput,
  dek: CryptoKey
): Promise<string> {
  try {
    const iv = base64ToArrayBuffer(input.iv);
    const encryptedData = base64ToArrayBuffer(input.encryptedData);
    const tag = base64ToArrayBuffer(input.tag);
    
    // Combine encrypted data and tag for WebCrypto
    const encryptedWithTag = new Uint8Array(encryptedData.byteLength + tag.byteLength);
    encryptedWithTag.set(new Uint8Array(encryptedData));
    encryptedWithTag.set(new Uint8Array(tag), encryptedData.byteLength);
    
    // Decrypt the data
    const decryptedBuffer = await crypto.subtle.decrypt(
      {
        name: 'AES-GCM',
        iv: iv,
        tagLength: 128,
      },
      dek,
      encryptedWithTag
    );
    
    return new TextDecoder().decode(decryptedBuffer);
  } catch (error) {
    throw new CryptoError(
      `Failed to decrypt with DEK: ${error instanceof Error ? error.message : 'Unknown error'}`,
      'decryptWithDek'
    );
  }
}

/**
 * Wrap (encrypt) a DEK using the Master Key
 */
export async function wrapDek(dek: CryptoKey, masterKey: string): Promise<string> {
  try {
    // Import the master key
    const masterKeyBytes = base64ToArrayBuffer(masterKey);
    const kek = await crypto.subtle.importKey(
      'raw',
      masterKeyBytes,
      { name: 'AES-GCM' },
      false,
      ['encrypt']
    );
    
    // Export the DEK as raw bytes
    const dekBytes = await crypto.subtle.exportKey('raw', dek);
    
    // Generate IV for wrapping
    const iv = crypto.getRandomValues(new Uint8Array(12));
    
    // Encrypt the DEK
    const wrappedBuffer = await crypto.subtle.encrypt(
      {
        name: 'AES-GCM',
        iv: iv,
        tagLength: 128,
      },
      kek,
      dekBytes
    );
    
    // Combine IV and wrapped DEK
    const combined = new Uint8Array(iv.length + wrappedBuffer.byteLength);
    combined.set(iv);
    combined.set(new Uint8Array(wrappedBuffer), iv.length);
    
    return arrayBufferToBase64(combined);
  } catch (error) {
    throw new CryptoError(
      `Failed to wrap DEK: ${error instanceof Error ? error.message : 'Unknown error'}`,
      'wrapDek'
    );
  }
}

/**
 * Unwrap (decrypt) a DEK using the Master Key
 */
export async function unwrapDek(wrappedDek: string, masterKey: string): Promise<CryptoKey> {
  try {
    // Import the master key
    const masterKeyBytes = base64ToArrayBuffer(masterKey);
    const kek = await crypto.subtle.importKey(
      'raw',
      masterKeyBytes,
      { name: 'AES-GCM' },
      false,
      ['decrypt']
    );
    
    // Parse the wrapped DEK
    const combined = base64ToArrayBuffer(wrappedDek);
    const iv = combined.slice(0, 12);
    const wrappedDekBytes = combined.slice(12);
    
    // Decrypt the DEK
    const dekBytes = await crypto.subtle.decrypt(
      {
        name: 'AES-GCM',
        iv: iv,
        tagLength: 128,
      },
      kek,
      wrappedDekBytes
    );
    
    // Import the unwrapped DEK
    return await crypto.subtle.importKey(
      'raw',
      dekBytes,
      { name: 'AES-GCM' },
      true,
      ['encrypt', 'decrypt']
    );
  } catch (error) {
    throw new CryptoError(
      `Failed to unwrap DEK: ${error instanceof Error ? error.message : 'Unknown error'}`,
      'unwrapDek'
    );
  }
}

/**
 * Complete envelope encryption: encrypt data with DEK, then wrap DEK with master key
 */
export async function envelopeEncrypt(
  data: string,
  masterKey: string
): Promise<EnvelopeEncryptionResult> {
  try {
    // Generate a new DEK for this encryption
    const dek = await generateDek();
    
    // Encrypt the data with the DEK
    const encryptionResult = await encryptWithDek(data, dek);
    
    // Wrap the DEK with the master key
    const encryptedDek = await wrapDek(dek, masterKey);
    
    return {
      ...encryptionResult,
      encryptedDek,
    };
  } catch (error) {
    throw new CryptoError(
      `Failed to envelope encrypt: ${error instanceof Error ? error.message : 'Unknown error'}`,
      'envelopeEncrypt'
    );
  }
}

/**
 * Complete envelope decryption: unwrap DEK with master key, then decrypt data with DEK
 */
export async function envelopeDecrypt(
  input: EnvelopeDecryptionInput,
  masterKey: string
): Promise<string> {
  try {
    // Unwrap the DEK using the master key
    const dek = await unwrapDek(input.encryptedDek, masterKey);
    
    // Decrypt the data using the DEK
    return await decryptWithDek(input, dek);
  } catch (error) {
    throw new CryptoError(
      `Failed to envelope decrypt: ${error instanceof Error ? error.message : 'Unknown error'}`,
      'envelopeDecrypt'
    );
  }
}

// Utility functions for base64 encoding/decoding
function arrayBufferToBase64(buffer: ArrayBuffer): string {
  const bytes = new Uint8Array(buffer);
  let binary = '';
  for (let i = 0; i < bytes.byteLength; i++) {
    binary += String.fromCharCode(bytes[i]);
  }
  return btoa(binary);
}

function base64ToArrayBuffer(base64: string): ArrayBuffer {
  const binary = atob(base64);
  const bytes = new Uint8Array(binary.length);
  for (let i = 0; i < binary.length; i++) {
    bytes[i] = binary.charCodeAt(i);
  }
  return bytes.buffer;
}

/**
 * Generate a random master key for testing/development
 * In production, this should be a securely generated key stored in environment variables
 */
export function generateMasterKey(): string {
  const key = crypto.getRandomValues(new Uint8Array(32)); // 256-bit key
  return arrayBufferToBase64(key);
}
