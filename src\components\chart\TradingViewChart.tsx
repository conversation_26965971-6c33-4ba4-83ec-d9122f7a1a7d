'use client';

import { useEffect, useRef, useState } from 'react';
import { createChart, ColorType, CandlestickSeries, CrosshairMode } from 'lightweight-charts';

interface DrawnLine {
  id: string;
  price: number;
  color: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  priceLine: any;
}

interface TradingViewChartProps {
  selectedTool: string;
  onToolSelect: (tool: string) => void;
  isDarkMode: boolean;
}

export default function TradingViewChart({ selectedTool, onToolSelect, isDarkMode }: TradingViewChartProps) {
  const chartContainerRef = useRef<HTMLDivElement>(null);
  const chartRef = useRef<ReturnType<typeof createChart> | null>(null);
  const candlestickSeriesRef = useRef<ReturnType<ReturnType<typeof createChart>['addSeries']> | null>(null);
  const currentCrosshairPriceRef = useRef<number | null>(null);
  const drawnLinesRef = useRef<DrawnLine[]>([]);
  
  const [editingLine, setEditingLine] = useState<DrawnLine | null>(null);
  const [editPrice, setEditPrice] = useState<string>('');
  const [editColor, setEditColor] = useState<string>('#2196F3');

  useEffect(() => {
    if (!chartContainerRef.current) return;

    const chart = createChart(chartContainerRef.current, {
      layout: {
        background: { type: ColorType.Solid, color: '#ffffff' },
        textColor: '#333',
      },
      width: chartContainerRef.current.clientWidth,
      height: chartContainerRef.current.clientHeight,
      grid: {
        vertLines: {
          color: '#e1e1e1',
        },
        horzLines: {
          color: '#e1e1e1',
        },
      },
      crosshair: {
        mode: CrosshairMode.MagnetOHLC, // MagnetOHLC mode that snaps to Open/High/Low/Close values
      },
      rightPriceScale: {
        borderColor: '#cccccc',
      },
      timeScale: {
        borderColor: '#cccccc',
      },
    });

    chartRef.current = chart;

    const candlestickSeries = chart.addSeries(CandlestickSeries, {
      upColor: '#26a69a',
      downColor: '#ef5350',
      borderVisible: false,
      wickUpColor: '#26a69a',
      wickDownColor: '#ef5350',
    });

    candlestickSeriesRef.current = candlestickSeries;

    // Sample data - replace with real data
    const sampleData = [
      { time: '2023-12-01' as const, open: 100, high: 105, low: 95, close: 102 },
      { time: '2023-12-02' as const, open: 102, high: 108, low: 100, close: 106 },
      { time: '2023-12-03' as const, open: 106, high: 110, low: 104, close: 108 },
      { time: '2023-12-04' as const, open: 108, high: 112, low: 106, close: 110 },
      { time: '2023-12-05' as const, open: 110, high: 115, low: 108, close: 113 },
    ];

    candlestickSeries.setData(sampleData);

    // Subscribe to crosshair move to track snapped prices
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const handleCrosshairMove = (param: any) => {
      if (param.seriesData && param.seriesData.has(candlestickSeries)) {
        const data = param.seriesData.get(candlestickSeries);
        if (data && typeof data === 'object' && 'open' in data && 'high' in data && 'low' in data && 'close' in data) {
          // Store the price that the crosshair is currently snapped to
          // For candlestick data, we need to determine which OHLC value is closest to cursor
          const prices = [data.open, data.high, data.low, data.close];
          
          // If we have mouse coordinates, find the closest price
          if (param.point) {
            const mousePrice = candlestickSeries.coordinateToPrice(param.point.y);
            if (mousePrice !== null && mousePrice !== undefined) {
              // Find the closest OHLC price to the mouse position
              const closestPrice = prices.reduce((prev: number, curr: number) => 
                Math.abs(curr - mousePrice) < Math.abs(prev - mousePrice) ? curr : prev
              );
              currentCrosshairPriceRef.current = closestPrice;
            }
          }
        }
      } else {
        currentCrosshairPriceRef.current = null;
      }
    };

    chart.subscribeCrosshairMove(handleCrosshairMove);

    const handleResize = () => {
      if (chartContainerRef.current) {
        chart.applyOptions({
          width: chartContainerRef.current.clientWidth,
          height: chartContainerRef.current.clientHeight,
        });
      }
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      chart.unsubscribeCrosshairMove(handleCrosshairMove);
      chart.remove();
    };
  }, []);

  // Handle theme changes without recreating the chart
  useEffect(() => {
    if (!chartRef.current) return;
    
    chartRef.current.applyOptions({
      layout: {
        background: { type: ColorType.Solid, color: isDarkMode ? '#111827' : '#ffffff' },
        textColor: isDarkMode ? '#d1d5db' : '#333',
      },
      grid: {
        vertLines: {
          color: isDarkMode ? '#374151' : '#e1e1e1',
        },
        horzLines: {
          color: isDarkMode ? '#374151' : '#e1e1e1',
        },
      },
      rightPriceScale: {
        borderColor: isDarkMode ? '#4b5563' : '#cccccc',
      },
      timeScale: {
        borderColor: isDarkMode ? '#4b5563' : '#cccccc',
      },
    });
  }, [isDarkMode]);

  // Handle chart clicks for drawing horizontal lines
  useEffect(() => {
    if (!chartRef.current || !chartContainerRef.current || !candlestickSeriesRef.current) return;

    const container = chartContainerRef.current;
    const series = candlestickSeriesRef.current;

    const handleChartClick = (event: MouseEvent) => {
      const rect = container.getBoundingClientRect();
      const y = event.clientY - rect.top;
      const clickedPrice = series.coordinateToPrice(y);
      
      // Check if user clicked on an existing line (within 5 pixels tolerance)
      if (selectedTool === 'crosshair' && clickedPrice !== null) {
        const tolerance = 5; // pixels
        const clickedLine = drawnLinesRef.current.find(line => {
          const lineY = series.priceToCoordinate(line.price);
          return lineY !== null && Math.abs(lineY - y) <= tolerance;
        });
        
        if (clickedLine) {
          // Open edit dialog for the clicked line
          setEditingLine(clickedLine);
          setEditPrice(clickedLine.price.toFixed(2));
          setEditColor(clickedLine.color);
          return;
        }
      }
      
      // Handle horizontal line drawing
      if (selectedTool !== 'horizontalLine') return;

      let price: number | null = null;
      
      // First, try to use the snapped crosshair price if available
      if (currentCrosshairPriceRef.current !== null) {
        price = currentCrosshairPriceRef.current;
      } else {
        // Fallback to mouse coordinate conversion
        price = clickedPrice;
      }
      
      if (price !== null && price !== undefined) {
        // Create horizontal line at the clicked price level
        const priceLine = series.createPriceLine({
          price: price,
          color: '#2196F3',
          lineWidth: 2,
          lineStyle: 0, // Solid line
          axisLabelVisible: true,
          title: `Level ${price.toFixed(2)}`,
        });
        
        // Store the drawn line for editing
        const lineId = `line_${Date.now()}_${Math.random()}`;
        const drawnLine: DrawnLine = {
          id: lineId,
          price: price,
          color: '#2196F3',
          priceLine: priceLine,
        };
        drawnLinesRef.current.push(drawnLine);
        
        // Switch back to crosshair mode after drawing the line
        onToolSelect('crosshair');
      }
    };

    container.addEventListener('click', handleChartClick);

    return () => {
      container.removeEventListener('click', handleChartClick);
    };
  }, [selectedTool, onToolSelect]);

  // Handle line editing functions
  const handleSaveEdit = () => {
    if (!editingLine || !candlestickSeriesRef.current) return;
    
    const newPrice = parseFloat(editPrice);
    if (isNaN(newPrice)) return;
    
    // Remove the old price line
    candlestickSeriesRef.current.removePriceLine(editingLine.priceLine);
    
    // Create new price line with updated values
    const newPriceLine = candlestickSeriesRef.current.createPriceLine({
      price: newPrice,
      color: editColor,
      lineWidth: 2,
      lineStyle: 0,
      axisLabelVisible: true,
      title: `Level ${newPrice.toFixed(2)}`,
    });
    
    // Update the stored line
    const lineIndex = drawnLinesRef.current.findIndex(line => line.id === editingLine.id);
    if (lineIndex !== -1) {
      drawnLinesRef.current[lineIndex] = {
        ...editingLine,
        price: newPrice,
        color: editColor,
        priceLine: newPriceLine,
      };
    }
    
    setEditingLine(null);
  };

  const handleDeleteLine = () => {
    if (!editingLine || !candlestickSeriesRef.current) return;
    
    // Remove the price line from chart
    candlestickSeriesRef.current.removePriceLine(editingLine.priceLine);
    
    // Remove from stored lines
    drawnLinesRef.current = drawnLinesRef.current.filter(line => line.id !== editingLine.id);
    
    setEditingLine(null);
  };

  const handleCancelEdit = () => {
    setEditingLine(null);
  };

  return (
    <div className={`h-full w-full relative ${isDarkMode ? 'bg-gray-900' : 'bg-white'}`}>
      <div ref={chartContainerRef} className={`w-full h-full ${isDarkMode ? 'bg-gray-900' : 'bg-white'}`} />
      
      {/* Edit Dialog */}
      {editingLine && (
        <div className={`absolute top-4 right-4 border rounded-lg shadow-lg p-4 z-10 min-w-[200px] ${
          isDarkMode 
            ? 'bg-gray-800 border-gray-600' 
            : 'bg-white border-gray-300'
        }`}>
          <h3 className={`text-sm font-semibold mb-3 ${isDarkMode ? 'text-gray-100' : 'text-gray-900'}`}>Edit Line</h3>
          
          {/* Price Input */}
          <div className="mb-3">
            <label htmlFor="edit-price" className={`block text-xs font-medium mb-1 ${isDarkMode ? 'text-gray-100' : 'text-gray-900'}`}>Price</label>
            <input
              id="edit-price"
              type="number"
              step="0.01"
              value={editPrice}
              onChange={(e) => setEditPrice(e.target.value)}
              placeholder="Enter price"
              className={`w-full px-2 py-1 text-sm border rounded focus:outline-none focus:ring-1 focus:ring-blue-500 ${
                isDarkMode 
                  ? 'border-gray-600 bg-gray-700 text-gray-100' 
                  : 'border-gray-300 bg-white text-gray-900'
              }`}
            />
          </div>
          
          {/* Color Input */}
          <div className="mb-4">
            <label className={`block text-xs font-medium mb-1 ${isDarkMode ? 'text-gray-100' : 'text-gray-900'}`}>Color</label>
            <div className="flex space-x-2">
              <button
                type="button"
                onClick={() => setEditColor('#2196F3')}
                title="Select Blue color"
                className={`w-6 h-6 rounded border-2 bg-blue-500 ${editColor === '#2196F3' ? 'border-gray-800 dark:border-gray-200' : 'border-gray-300'}`}
              />
              <button
                type="button"
                onClick={() => setEditColor('#4CAF50')}
                title="Select Green color"
                className={`w-6 h-6 rounded border-2 bg-green-500 ${editColor === '#4CAF50' ? 'border-gray-800 dark:border-gray-200' : 'border-gray-300'}`}
              />
              <button
                type="button"
                onClick={() => setEditColor('#FF9800')}
                title="Select Orange color"
                className={`w-6 h-6 rounded border-2 bg-orange-500 ${editColor === '#FF9800' ? 'border-gray-800 dark:border-gray-200' : 'border-gray-300'}`}
              />
              <button
                type="button"
                onClick={() => setEditColor('#F44336')}
                title="Select Red color"
                className={`w-6 h-6 rounded border-2 bg-red-500 ${editColor === '#F44336' ? 'border-gray-800 dark:border-gray-200' : 'border-gray-300'}`}
              />
              <button
                type="button"
                onClick={() => setEditColor('#9C27B0')}
                title="Select Purple color"
                className={`w-6 h-6 rounded border-2 bg-purple-500 ${editColor === '#9C27B0' ? 'border-gray-800 dark:border-gray-200' : 'border-gray-300'}`}
              />
            </div>
          </div>
          
          {/* Action Buttons */}
          <div className="flex space-x-2">
            <button
              type="button"
              onClick={handleSaveEdit}
              className="flex-1 px-3 py-1 text-xs bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors"
            >
              Save
            </button>
            <button
              type="button"
              onClick={handleDeleteLine}
              className="flex-1 px-3 py-1 text-xs bg-red-600 hover:bg-red-700 text-white rounded transition-colors"
            >
              Delete
            </button>
            <button
              type="button"
              onClick={handleCancelEdit}
              className="flex-1 px-3 py-1 text-xs bg-gray-600 hover:bg-gray-700 text-white rounded transition-colors"
            >
              Cancel
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
