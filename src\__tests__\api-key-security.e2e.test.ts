/**
 * End-to-End Security Tests for API Key Storage
 * 
 * These tests verify that:
 * 1. API secrets are never stored in plaintext
 * 2. API secrets are never returned to the client
 * 3. The complete save → read masked → delete flow works correctly
 * 4. Encryption/decryption roundtrip works with real crypto
 */

import { envelopeEncrypt, envelopeDecrypt, generateMasterKey } from '@/lib/crypto/envelope';

// Mock fetch for API calls
global.fetch = jest.fn();

describe('API Key Security End-to-End Tests', () => {
  const testApiKey = 'ABCDEFGHIJ1234567890';
  const testApiSecret = 'abcdefghijklmnopqrstuvwxyz1234567890ABCD';
  const testMasterKey = generateMasterKey();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Crypto Roundtrip with Real WebCrypto', () => {
    // Skip if WebCrypto is not available (Node.js environment)
    const isWebCryptoAvailable = typeof crypto !== 'undefined' && crypto.subtle;

    (isWebCryptoAvailable ? describe : describe.skip)('Real WebCrypto Tests', () => {
      it('should successfully encrypt and decrypt API secret', async () => {
        // Encrypt the API secret
        const encrypted = await envelopeEncrypt(testApiSecret, testMasterKey);

        // Verify encrypted data structure
        expect(encrypted).toHaveProperty('encryptedData');
        expect(encrypted).toHaveProperty('iv');
        expect(encrypted).toHaveProperty('tag');
        expect(encrypted).toHaveProperty('encryptedDek');

        // Verify all fields are base64 strings
        expect(typeof encrypted.encryptedData).toBe('string');
        expect(typeof encrypted.iv).toBe('string');
        expect(typeof encrypted.tag).toBe('string');
        expect(typeof encrypted.encryptedDek).toBe('string');

        // Verify encrypted data is different from original
        expect(encrypted.encryptedData).not.toBe(testApiSecret);
        expect(atob(encrypted.encryptedData)).not.toBe(testApiSecret);

        // Decrypt the API secret
        const decrypted = await envelopeDecrypt(encrypted, testMasterKey);

        // Verify decryption worked
        expect(decrypted).toBe(testApiSecret);
      });

      it('should fail decryption with wrong master key', async () => {
        const encrypted = await envelopeEncrypt(testApiSecret, testMasterKey);
        const wrongMasterKey = generateMasterKey();

        await expect(envelopeDecrypt(encrypted, wrongMasterKey)).rejects.toThrow();
      });

      it('should fail decryption with tampered data', async () => {
        const encrypted = await envelopeEncrypt(testApiSecret, testMasterKey);
        
        // Tamper with encrypted data
        const tamperedEncrypted = {
          ...encrypted,
          encryptedData: btoa('tampered-data'),
        };

        await expect(envelopeDecrypt(tamperedEncrypted, testMasterKey)).rejects.toThrow();
      });

      it('should generate unique encryption results for same input', async () => {
        const encrypted1 = await envelopeEncrypt(testApiSecret, testMasterKey);
        const encrypted2 = await envelopeEncrypt(testApiSecret, testMasterKey);

        // Should have different IVs and encrypted data (due to random IV)
        expect(encrypted1.iv).not.toBe(encrypted2.iv);
        expect(encrypted1.encryptedData).not.toBe(encrypted2.encryptedData);
        expect(encrypted1.encryptedDek).not.toBe(encrypted2.encryptedDek);

        // But both should decrypt to the same value
        const decrypted1 = await envelopeDecrypt(encrypted1, testMasterKey);
        const decrypted2 = await envelopeDecrypt(encrypted2, testMasterKey);

        expect(decrypted1).toBe(testApiSecret);
        expect(decrypted2).toBe(testApiSecret);
      });
    });
  });

  describe('API Security Flow', () => {
    it('should never expose plaintext secrets in API responses', async () => {
      // Mock successful POST response
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          provider: 'alpaca',
          last4: '7890',
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
        }),
      });

      // Simulate saving API key
      const saveResponse = await fetch('/api/user/api-keys', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-token',
        },
        body: JSON.stringify({
          provider: 'alpaca',
          apiKey: testApiKey,
          apiSecret: testApiSecret,
        }),
      });

      const saveData = await saveResponse.json();

      // Verify response contains only safe metadata
      expect(saveData).toHaveProperty('provider');
      expect(saveData).toHaveProperty('last4');
      expect(saveData).toHaveProperty('created_at');
      expect(saveData).toHaveProperty('updated_at');

      // Verify response does NOT contain sensitive data
      expect(saveData).not.toHaveProperty('apiKey');
      expect(saveData).not.toHaveProperty('apiSecret');
      expect(saveData).not.toHaveProperty('encrypted_key');
      expect(saveData).not.toHaveProperty('iv');
      expect(saveData).not.toHaveProperty('tag');
      expect(saveData).not.toHaveProperty('encrypted_dek');

      // Verify last4 is correctly masked
      expect(saveData.last4).toBe('7890');
      expect(saveData.last4).not.toBe(testApiKey);
    });

    it('should return only masked metadata on GET requests', async () => {
      // Mock successful GET response
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          provider: 'alpaca',
          last4: '7890',
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
        }),
      });

      // Simulate fetching API key metadata
      const getResponse = await fetch('/api/user/api-keys?provider=alpaca', {
        method: 'GET',
        headers: {
          'Authorization': 'Bearer test-token',
        },
      });

      const getData = await getResponse.json();

      // Verify response contains only safe metadata
      expect(getData).toEqual({
        provider: 'alpaca',
        last4: '7890',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      });

      // Verify no sensitive data is present
      expect(getData).not.toHaveProperty('encrypted_key');
      expect(getData).not.toHaveProperty('iv');
      expect(getData).not.toHaveProperty('tag');
      expect(getData).not.toHaveProperty('encrypted_dek');
      expect(getData).not.toHaveProperty('apiKey');
      expect(getData).not.toHaveProperty('apiSecret');
    });

    it('should successfully delete API keys', async () => {
      // Mock successful DELETE response
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true }),
      });

      // Simulate deleting API key
      const deleteResponse = await fetch('/api/user/api-keys?provider=alpaca', {
        method: 'DELETE',
        headers: {
          'Authorization': 'Bearer test-token',
        },
      });

      const deleteData = await deleteResponse.json();

      expect(deleteResponse.ok).toBe(true);
      expect(deleteData).toEqual({ success: true });
    });

    it('should handle complete save → read → delete flow', async () => {
      // Mock save response
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          provider: 'alpaca',
          last4: '7890',
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
        }),
      });

      // Mock read response
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          provider: 'alpaca',
          last4: '7890',
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
        }),
      });

      // Mock delete response
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true }),
      });

      // 1. Save API key
      const saveResponse = await fetch('/api/user/api-keys', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-token',
        },
        body: JSON.stringify({
          provider: 'alpaca',
          apiKey: testApiKey,
          apiSecret: testApiSecret,
        }),
      });

      expect(saveResponse.ok).toBe(true);
      const saveData = await saveResponse.json();
      expect(saveData.last4).toBe('7890');

      // 2. Read masked metadata
      const readResponse = await fetch('/api/user/api-keys?provider=alpaca', {
        method: 'GET',
        headers: {
          'Authorization': 'Bearer test-token',
        },
      });

      expect(readResponse.ok).toBe(true);
      const readData = await readResponse.json();
      expect(readData.last4).toBe('7890');
      expect(readData).not.toHaveProperty('encrypted_key');

      // 3. Delete API key
      const deleteResponse = await fetch('/api/user/api-keys?provider=alpaca', {
        method: 'DELETE',
        headers: {
          'Authorization': 'Bearer test-token',
        },
      });

      expect(deleteResponse.ok).toBe(true);
      const deleteData = await deleteResponse.json();
      expect(deleteData.success).toBe(true);
    });
  });

  describe('Input Validation Security', () => {
    it('should reject malformed API keys', async () => {
      const malformedKeys = [
        'too-short',
        'TOOLONGAPIKEYWITHMORETHAN20CHARS',
        'invalid-chars-!@#$%',
        '',
        null,
        undefined,
      ];

      for (const badKey of malformedKeys) {
        (fetch as jest.Mock).mockResolvedValueOnce({
          ok: false,
          status: 400,
          json: async () => ({ error: 'Invalid API key format' }),
        });

        const response = await fetch('/api/user/api-keys', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer test-token',
          },
          body: JSON.stringify({
            provider: 'alpaca',
            apiKey: badKey,
            apiSecret: testApiSecret,
          }),
        });

        expect(response.ok).toBe(false);
        const data = await response.json();
        expect(data.error).toBe('Invalid API key format');
      }
    });

    it('should reject malformed API secrets', async () => {
      const malformedSecrets = [
        'too-short',
        'TOOLONGAPISECRETWITHMORETHAN40CHARACTERS',
        'invalid-chars-!@#$%^&*()',
        '',
        null,
        undefined,
      ];

      for (const badSecret of malformedSecrets) {
        (fetch as jest.Mock).mockResolvedValueOnce({
          ok: false,
          status: 400,
          json: async () => ({ error: 'Invalid API secret format' }),
        });

        const response = await fetch('/api/user/api-keys', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer test-token',
          },
          body: JSON.stringify({
            provider: 'alpaca',
            apiKey: testApiKey,
            apiSecret: badSecret,
          }),
        });

        expect(response.ok).toBe(false);
        const data = await response.json();
        expect(data.error).toBe('Invalid API secret format');
      }
    });
  });
});
