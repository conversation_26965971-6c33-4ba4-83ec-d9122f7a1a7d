import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

const supabaseAuth = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');
    console.log('Auth header:', authHeader);
    
    if (!authHeader?.startsWith('Bearer ')) {
      return NextResponse.json({ error: 'Missing authorization header' }, { status: 401 });
    }

    const token = authHeader.substring(7);
    console.log('Token length:', token.length);
    console.log('Token preview:', token.substring(0, 20) + '...');

    const { data: { user }, error } = await supabaseAuth.auth.getUser(token);
    
    console.log('Supabase auth result:', { user: user?.id, error: error?.message });

    if (error) {
      return NextResponse.json({ 
        error: 'Token validation failed', 
        details: error.message 
      }, { status: 401 });
    }

    if (!user?.id) {
      return NextResponse.json({ error: 'No user found' }, { status: 401 });
    }

    return NextResponse.json({ 
      success: true, 
      userId: user.id,
      email: user.email 
    });

  } catch (error) {
    console.error('Auth test error:', error);
    return NextResponse.json({ 
      error: 'Internal error', 
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}
