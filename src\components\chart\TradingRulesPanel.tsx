interface TradingRulesPanelProps {
  isDarkMode: boolean;
}

export default function TradingRulesPanel({ isDarkMode }: TradingRulesPanelProps) {
  return (
    <div className={`h-full p-6 ${isDarkMode ? 'bg-gray-900' : 'bg-white'}`}>
      <div className={`h-full border rounded-lg p-4 ${
        isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-gray-50'
      }`}>
        <h2 className={`text-xl font-semibold mb-4 ${isDarkMode ? 'text-gray-100' : 'text-gray-900'}`}>
          Trading Rules
        </h2>
        
        <div className={`space-y-4 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
          <div>
            <h3 className={`text-lg font-medium mb-2 ${isDarkMode ? 'text-gray-200' : 'text-gray-800'}`}>
              Risk Management
            </h3>
            <p>
              This is placeholder content for trading rules. Here you would define your risk management strategies, 
              position sizing rules, and entry/exit criteria.
            </p>
          </div>
          
          <div>
            <h3 className={`text-lg font-medium mb-2 ${isDarkMode ? 'text-gray-200' : 'text-gray-800'}`}>
              Entry Conditions
            </h3>
            <p>
              Define your entry conditions and setup requirements. This could include technical indicators, 
              market conditions, and timing requirements.
            </p>
          </div>
          
          <div>
            <h3 className={`text-lg font-medium mb-2 ${isDarkMode ? 'text-gray-200' : 'text-gray-800'}`}>
              Exit Strategy
            </h3>
            <p>
              Outline your exit strategy including profit targets, stop losses, and position management rules. 
              This helps maintain discipline and consistency in your trading approach.
            </p>
          </div>
          
          <div className={`mt-6 p-4 rounded-lg ${
            isDarkMode ? 'bg-gray-700 border border-gray-600' : 'bg-blue-50 border border-blue-200'
          }`}>
            <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-blue-800'}`}>
              💡 <strong>Tip:</strong> Click the &quot;Trading Rules&quot; tab again to collapse this panel and return to the chart view.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
