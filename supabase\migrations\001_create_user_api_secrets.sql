-- Create user_api_secrets table for secure API key storage with envelope encryption
CREATE TABLE user_api_secrets (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    provider TEXT NOT NULL CHECK (provider IN ('alpaca')),
    encrypted_key TEXT NOT NULL, -- The encrypted API key
    iv TEXT NOT NULL, -- Initialization vector for AES-GCM
    tag TEXT NOT NULL, -- Authentication tag for AES-GCM
    encrypted_dek TEXT NOT NULL, -- Data Encryption Key encrypted with Master Key
    api_key_last4 TEXT NOT NULL CHECK (length(api_key_last4) = 4), -- Last 4 chars for display
    created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
    
    -- Ensure one API key per user per provider
    UNIQUE(user_id, provider)
);

-- Create index for efficient lookups
CREATE INDEX idx_user_api_secrets_user_provider ON user_api_secrets(user_id, provider);

-- Enable Row Level Security
ALTER TABLE user_api_secrets ENABLE ROW LEVEL SECURITY;

-- RLS Policy: Users can only read metadata of their own API keys (no encrypted data)
CREATE POLICY "Users can read own API key metadata" ON user_api_secrets
    FOR SELECT
    USING (auth.uid() = user_id);

-- RLS Policy: Users cannot insert directly (only through server functions)
CREATE POLICY "No direct inserts allowed" ON user_api_secrets
    FOR INSERT
    WITH CHECK (false);

-- RLS Policy: Users cannot update directly (only through server functions)
CREATE POLICY "No direct updates allowed" ON user_api_secrets
    FOR UPDATE
    USING (false);

-- RLS Policy: Users can delete their own API keys
CREATE POLICY "Users can delete own API keys" ON user_api_secrets
    FOR DELETE
    USING (auth.uid() = user_id);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_user_api_secrets_updated_at
    BEFORE UPDATE ON user_api_secrets
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Grant necessary permissions to service role
GRANT ALL ON user_api_secrets TO service_role;
GRANT USAGE ON SCHEMA public TO service_role;
