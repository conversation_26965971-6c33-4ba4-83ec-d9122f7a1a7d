import ChartToolbar from './chart/ChartToolbar';
import TradingViewChart from './chart/TradingViewChart';
import TradingRulesPanel from './chart/TradingRulesPanel';
import BottomToolbar from './chart/BottomToolbar';

interface ChartSectionProps {
  selectedTool: string;
  onToolSelect: (tool: string) => void;
  isDarkMode: boolean;
  isTradingRulesExpanded: boolean;
  onToggleTradingRules: () => void;
}

export default function ChartSection({ 
  selectedTool, 
  onToolSelect, 
  isDarkMode, 
  isTradingRulesExpanded, 
  onToggleTradingRules 
}: ChartSectionProps) {
  return (
    <div className={`w-4/5 flex ${isDarkMode ? 'bg-gray-900' : 'bg-white'}`}>
      {/* Left Toolbar - Full Height */}
      <ChartToolbar selectedTool={selectedTool} onToolSelect={onToolSelect} isDarkMode={isDarkMode} />
      
      {/* Chart and Bottom Toolbar Area */}
      <div className="flex-1 flex flex-col">
        {/* Chart Area */}
        <div className={`flex-1 relative ${isDarkMode ? 'bg-gray-900 border-gray-900' : 'bg-white border-white'}`}>
          {/* Chart - Always rendered but hidden when rules expanded */}
          <div className={`absolute inset-0 ${isTradingRulesExpanded ? 'hidden' : 'block'}`}>
            <TradingViewChart selectedTool={selectedTool} onToolSelect={onToolSelect} isDarkMode={isDarkMode} />
          </div>
          
          {/* Trading Rules Panel - Always rendered but hidden when not expanded */}
          <div className={`absolute inset-0 ${isTradingRulesExpanded ? 'block' : 'hidden'}`}>
            <TradingRulesPanel isDarkMode={isDarkMode} />
          </div>
        </div>
        
        {/* Bottom Toolbar */}
        <BottomToolbar 
          isDarkMode={isDarkMode} 
          isTradingRulesExpanded={isTradingRulesExpanded}
          onToggleTradingRules={onToggleTradingRules}
        />
      </div>
    </div>
  );
}
