/**
 * Security utilities for API key management
 */

// Input sanitization and validation
export function sanitizeString(input: string): string {
  if (typeof input !== 'string') {
    throw new Error('Input must be a string');
  }
  
  // Remove any null bytes and control characters except newlines/tabs
  return input.replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '');
}

// Validate API key format for different providers
export function validateApiKeyFormat(provider: string, apiKey: string): boolean {
  if (!apiKey || typeof apiKey !== 'string') {
    return false;
  }

  const sanitized = sanitizeString(apiKey.trim());
  
  switch (provider) {
    case 'alpaca':
      // Alpaca API keys are 20 characters, alphanumeric uppercase
      return /^[A-Z0-9]{20}$/.test(sanitized);
    default:
      return false;
  }
}

// Validate API secret format for different providers
export function validateApiSecretFormat(provider: string, apiSecret: string): boolean {
  if (!apiSecret || typeof apiSecret !== 'string') {
    return false;
  }

  const sanitized = sanitizeString(apiSecret.trim());
  
  switch (provider) {
    case 'alpaca':
      // Alpaca API secrets are 40 characters, base64-like
      return /^[A-Za-z0-9+/]{40}$/.test(sanitized);
    default:
      return false;
  }
}

// Validate provider name
export function validateProvider(provider: string): boolean {
  if (!provider || typeof provider !== 'string') {
    return false;
  }

  const sanitized = sanitizeString(provider.trim().toLowerCase());
  const allowedProviders = ['alpaca'];
  
  return allowedProviders.includes(sanitized);
}

// Extract last N characters safely
export function getLastNChars(str: string, n: number): string {
  if (!str || typeof str !== 'string' || n <= 0) {
    return '';
  }
  
  const sanitized = sanitizeString(str);
  return sanitized.slice(-n);
}

// Secure error response that doesn't leak sensitive information
export interface SecureErrorResponse {
  error: string;
  code?: string;
  timestamp: string;
}

export function createSecureErrorResponse(
  message: string, 
  code?: string,
  logDetails?: string
): SecureErrorResponse {
  // Log detailed error for debugging (server-side only)
  if (logDetails && typeof console !== 'undefined') {
    console.error(`[API Error] ${code || 'UNKNOWN'}: ${logDetails}`);
  }

  // Return sanitized error to client
  return {
    error: sanitizeString(message),
    ...(code && { code }),
    timestamp: new Date().toISOString(),
  };
}

// Request validation
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
}

export function validateApiKeyRequest(body: any): ValidationResult {
  const errors: string[] = [];

  // Check required fields
  if (!body) {
    errors.push('Request body is required');
    return { isValid: false, errors };
  }

  if (!body.provider) {
    errors.push('Provider is required');
  } else if (!validateProvider(body.provider)) {
    errors.push('Invalid provider');
  }

  if (!body.apiKey) {
    errors.push('API key is required');
  } else if (!validateApiKeyFormat(body.provider, body.apiKey)) {
    errors.push('Invalid API key format');
  }

  if (!body.apiSecret) {
    errors.push('API secret is required');
  } else if (!validateApiSecretFormat(body.provider, body.apiSecret)) {
    errors.push('Invalid API secret format');
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

// Content Security Policy headers
export function getSecurityHeaders(): Record<string, string> {
  return {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:; frame-ancestors 'none';",
  };
}

// Rate limiting key generation
export function generateRateLimitKey(
  identifier: string, 
  endpoint: string, 
  method: string
): string {
  const sanitizedIdentifier = sanitizeString(identifier || 'anonymous');
  const sanitizedEndpoint = sanitizeString(endpoint);
  const sanitizedMethod = sanitizeString(method.toUpperCase());
  
  return `${sanitizedIdentifier}:${sanitizedEndpoint}:${sanitizedMethod}`;
}

// Audit logging (without sensitive data)
export interface AuditLogEntry {
  timestamp: string;
  userId?: string;
  action: string;
  provider?: string;
  success: boolean;
  ip?: string;
  userAgent?: string;
  error?: string;
}

export function createAuditLog(
  action: string,
  success: boolean,
  options: {
    userId?: string;
    provider?: string;
    ip?: string;
    userAgent?: string;
    error?: string;
  } = {}
): AuditLogEntry {
  return {
    timestamp: new Date().toISOString(),
    action: sanitizeString(action),
    success,
    ...(options.userId && { userId: sanitizeString(options.userId) }),
    ...(options.provider && { provider: sanitizeString(options.provider) }),
    ...(options.ip && { ip: sanitizeString(options.ip) }),
    ...(options.userAgent && { userAgent: sanitizeString(options.userAgent.substring(0, 200)) }), // Limit length
    ...(options.error && { error: sanitizeString(options.error) }),
  };
}

// Log audit entry (in production, this would go to a secure logging service)
export function logAuditEntry(entry: AuditLogEntry): void {
  // In development, log to console
  if (process.env.NODE_ENV === 'development') {
    console.log('[AUDIT]', JSON.stringify(entry, null, 2));
  }
  
  // In production, send to secure logging service
  // This could be AWS CloudWatch, Datadog, etc.
  // Example:
  // await secureLogger.log(entry);
}

// Timing attack prevention - constant time string comparison
export function constantTimeEquals(a: string, b: string): boolean {
  if (a.length !== b.length) {
    return false;
  }

  let result = 0;
  for (let i = 0; i < a.length; i++) {
    result |= a.charCodeAt(i) ^ b.charCodeAt(i);
  }

  return result === 0;
}

// Generate secure random string for testing/development
export function generateSecureRandomString(length: number): string {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';
  let result = '';
  
  if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
    const randomBytes = new Uint8Array(length);
    crypto.getRandomValues(randomBytes);
    
    for (let i = 0; i < length; i++) {
      result += chars[randomBytes[i] % chars.length];
    }
  } else {
    // Fallback for environments without crypto.getRandomValues
    for (let i = 0; i < length; i++) {
      result += chars[Math.floor(Math.random() * chars.length)];
    }
  }
  
  return result;
}

// Environment validation
export function validateEnvironmentVariables(): void {
  const required = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY',
    'MASTER_KEY',
  ];

  const missing = required.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }

  // Validate master key format
  const masterKey = process.env.MASTER_KEY!;
  try {
    const decoded = atob(masterKey);
    if (decoded.length !== 32) {
      throw new Error('MASTER_KEY must be a base64-encoded 32-byte key');
    }
  } catch (error) {
    throw new Error('MASTER_KEY must be a valid base64-encoded string');
  }
}
