interface BottomToolbarProps {
  isDarkMode: boolean;
  isTradingRulesExpanded: boolean;
  onToggleTradingRules: () => void;
}

export default function BottomToolbar({ isDarkMode, isTradingRulesExpanded, onToggleTradingRules }: BottomToolbarProps) {
  return (
    <div className={`h-10 border-t-2 flex items-center px-4 ${
      isDarkMode 
        ? 'bg-gray-800 border-t-gray-700' 
        : 'bg-gray-100 border-t-gray-200'
    }`}>
      <div className="flex space-x-1">
        <button
          type="button"
          onClick={onToggleTradingRules}
          className={`px-3 py-1 text-sm rounded transition-colors ${
            isTradingRulesExpanded
              ? isDarkMode 
                ? 'bg-blue-900 text-blue-100' 
                : 'bg-blue-100 text-blue-900'
              : isDarkMode
                ? 'hover:bg-gray-700 text-gray-300'
                : 'hover:bg-gray-200 text-gray-700'
          }`}
        >
          Trading Rules
        </button>
      </div>
    </div>
  );
}
