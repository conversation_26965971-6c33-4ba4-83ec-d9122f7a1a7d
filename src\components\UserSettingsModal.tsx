'use client';

import { useState, useEffect } from 'react';
import { useAuth } from './AuthProvider';
import { supabase } from '../lib/supabase/browserClient';

interface UserSettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
  isDarkMode: boolean;
}

interface FormData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

interface FormErrors {
  currentPassword?: string;
  newPassword?: string;
  confirmPassword?: string;
  general?: string;
}

interface ApiKeyFormData {
  apiKey: string;
  apiSecret: string;
}

interface ApiKeyFormErrors {
  apiKey?: string;
  apiSecret?: string;
  general?: string;
}

interface ApiKeyMetadata {
  provider: string;
  last4: string;
  created_at: string;
  updated_at: string;
}

export default function UserSettingsModal({ isOpen, onClose, isDarkMode }: UserSettingsModalProps) {
  const { user, changePassword, signIn } = useAuth();
  const [formData, setFormData] = useState<FormData>({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });
  const [errors, setErrors] = useState<FormErrors>({});
  const [loading, setLoading] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  // API Key management state
  const [apiKeyFormData, setApiKeyFormData] = useState<ApiKeyFormData>({
    apiKey: '',
    apiSecret: '',
  });
  const [apiKeyErrors, setApiKeyErrors] = useState<ApiKeyFormErrors>({});
  const [apiKeyLoading, setApiKeyLoading] = useState(false);
  const [apiKeySuccessMessage, setApiKeySuccessMessage] = useState('');
  const [apiKeyMetadata, setApiKeyMetadata] = useState<ApiKeyMetadata | null>(null);
  const [showApiKeyForm, setShowApiKeyForm] = useState(false);

  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!formData.currentPassword) {
      newErrors.currentPassword = 'Current password is required';
    }

    if (!formData.newPassword) {
      newErrors.newPassword = 'New password is required';
    } else if (formData.newPassword.length < 6) {
      newErrors.newPassword = 'New password must be at least 6 characters';
    }

    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your new password';
    } else if (formData.newPassword !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    if (formData.currentPassword === formData.newPassword) {
      newErrors.newPassword = 'New password must be different from current password';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) return;

    setLoading(true);
    setErrors({});
    setSuccessMessage('');

    try {
      // First, verify the current password by attempting to sign in
      if (user?.email) {
        const verifyResult = await signIn(user.email, formData.currentPassword);

        if (verifyResult.error) {
          setErrors({ currentPassword: 'Current password is incorrect' });
          setLoading(false);
          return;
        }
      }

      // If current password is verified, proceed to change password
      const result = await changePassword(formData.newPassword);

      if (result.error) {
        setErrors({ general: result.error.message });
      } else {
        setSuccessMessage('Password changed successfully!');
        setFormData({
          currentPassword: '',
          newPassword: '',
          confirmPassword: '',
        });
        // Close modal after 2 seconds
        setTimeout(() => {
          onClose();
          setSuccessMessage('');
        }, 2000);
      }
    } catch {
      setErrors({ general: 'An unexpected error occurred. Please try again.' });
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear field-specific error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  // Load API key metadata when modal opens
  useEffect(() => {
    if (isOpen && user) {
      loadApiKeyMetadata();
    }
  }, [isOpen, user]);

  const loadApiKeyMetadata = async () => {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      console.log('Session in loadApiKeyMetadata:', {
        hasSession: !!session,
        hasAccessToken: !!session?.access_token,
        tokenLength: session?.access_token?.length
      });

      if (!session?.access_token) return;

      const response = await fetch('/api/user/api-keys?provider=alpaca', {
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setApiKeyMetadata(data);
      } else if (response.status !== 404) {
        console.error('Failed to load API key metadata');
      }
    } catch (error) {
      console.error('Error loading API key metadata:', error);
    }
  };

  const handleApiKeyInputChange = (field: keyof ApiKeyFormData, value: string) => {
    setApiKeyFormData(prev => ({ ...prev, [field]: value }));
    // Clear field-specific error when user starts typing
    if (apiKeyErrors[field]) {
      setApiKeyErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const validateApiKeyForm = (): boolean => {
    const newErrors: ApiKeyFormErrors = {};

    if (!apiKeyFormData.apiKey.trim()) {
      newErrors.apiKey = 'API Key is required';
    } else if (!/^[A-Z0-9]{20}$/.test(apiKeyFormData.apiKey.trim())) {
      newErrors.apiKey = 'Invalid API Key format (should be 20 alphanumeric characters)';
    }

    if (!apiKeyFormData.apiSecret.trim()) {
      newErrors.apiSecret = 'API Secret is required';
    } else if (!/^[A-Za-z0-9+/]{40}$/.test(apiKeyFormData.apiSecret.trim())) {
      newErrors.apiSecret = 'Invalid API Secret format (should be 40 characters)';
    }

    setApiKeyErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleApiKeySubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateApiKeyForm()) {
      return;
    }

    setApiKeyLoading(true);
    setApiKeyErrors({});

    try {
      const { data: { session } } = await supabase.auth.getSession();
      console.log('Session in handleApiKeySubmit:', {
        hasSession: !!session,
        hasAccessToken: !!session?.access_token,
        tokenLength: session?.access_token?.length
      });

      if (!session?.access_token) {
        setApiKeyErrors({ general: 'Authentication required' });
        return;
      }

      const response = await fetch('/api/user/api-keys', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
        body: JSON.stringify({
          provider: 'alpaca',
          apiKey: apiKeyFormData.apiKey.trim(),
          apiSecret: apiKeyFormData.apiSecret.trim(),
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setApiKeySuccessMessage('API key saved successfully!');
        setApiKeyFormData({ apiKey: '', apiSecret: '' });
        setShowApiKeyForm(false);
        await loadApiKeyMetadata(); // Reload metadata

        // Clear success message after 3 seconds
        setTimeout(() => {
          setApiKeySuccessMessage('');
        }, 3000);
      } else {
        setApiKeyErrors({ general: data.error || 'Failed to save API key' });
      }
    } catch (error) {
      setApiKeyErrors({ general: 'An unexpected error occurred. Please try again.' });
    } finally {
      setApiKeyLoading(false);
    }
  };

  const handleDeleteApiKey = async () => {
    if (!confirm('Are you sure you want to delete your API key? This action cannot be undone.')) {
      return;
    }

    setApiKeyLoading(true);

    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session?.access_token) {
        setApiKeyErrors({ general: 'Authentication required' });
        return;
      }

      const response = await fetch('/api/user/api-keys?provider=alpaca', {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${session.access_token}`,
        },
      });

      if (response.ok) {
        setApiKeySuccessMessage('API key deleted successfully!');
        setApiKeyMetadata(null);
        setShowApiKeyForm(false);

        // Clear success message after 3 seconds
        setTimeout(() => {
          setApiKeySuccessMessage('');
        }, 3000);
      } else {
        const data = await response.json();
        setApiKeyErrors({ general: data.error || 'Failed to delete API key' });
      }
    } catch (error) {
      setApiKeyErrors({ general: 'An unexpected error occurred. Please try again.' });
    } finally {
      setApiKeyLoading(false);
    }
  };

  const handleClose = () => {
    setFormData({
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    });
    setErrors({});
    setSuccessMessage('');

    // Reset API key form state
    setApiKeyFormData({
      apiKey: '',
      apiSecret: '',
    });
    setApiKeyErrors({});
    setApiKeySuccessMessage('');
    setShowApiKeyForm(false);

    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className={`rounded-lg shadow-xl w-full max-w-md mx-4 ${isDarkMode ? 'bg-gray-800' : 'bg-white'}`}>
        {/* Header */}
        <div className={`flex items-center justify-between p-6 border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
          <h2 className={`text-lg font-semibold ${isDarkMode ? 'text-gray-100' : 'text-gray-900'}`}>
            User Settings
          </h2>
          <button
            type="button"
            onClick={handleClose}
            aria-label="Close settings modal"
            title="Close settings modal"
            className={`p-1 rounded-lg transition-colors ${
              isDarkMode
                ? 'hover:bg-gray-700 text-gray-400 hover:text-gray-200'
                : 'hover:bg-gray-100 text-gray-500 hover:text-gray-700'
            }`}
          >
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" aria-hidden="true">
              <path d="M18 6L6 18M6 6l12 12"/>
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* User Info */}
          <div className={`mb-6 p-4 rounded-lg ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
            <div className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
              Logged in as:
            </div>
            <div className={`font-medium ${isDarkMode ? 'text-gray-100' : 'text-gray-900'}`}>
              {user?.email}
            </div>
          </div>

          {/* Change Password Form */}
          <form onSubmit={handleSubmit} className="space-y-4">
            <h3 className={`text-md font-medium mb-4 ${isDarkMode ? 'text-gray-100' : 'text-gray-900'}`}>
              Change Password
            </h3>

            {/* Success Message */}
            {successMessage && (
              <div className="p-3 bg-green-100 border border-green-400 text-green-700 rounded-lg text-sm">
                {successMessage}
              </div>
            )}

            {/* General Error */}
            {errors.general && (
              <div className="p-3 bg-red-100 border border-red-400 text-red-700 rounded-lg text-sm">
                {errors.general}
              </div>
            )}

            {/* Current Password */}
            <div>
              <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                Current Password
              </label>
              <input
                type="password"
                value={formData.currentPassword}
                onChange={(e) => handleInputChange('currentPassword', e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  isDarkMode 
                    ? 'bg-gray-700 border-gray-600 text-gray-100 placeholder-gray-400' 
                    : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                } ${errors.currentPassword ? 'border-red-500' : ''}`}
                placeholder="Enter your current password"
              />
              {errors.currentPassword && (
                <p className="mt-1 text-sm text-red-600">{errors.currentPassword}</p>
              )}
            </div>

            {/* New Password */}
            <div>
              <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                New Password
              </label>
              <input
                type="password"
                value={formData.newPassword}
                onChange={(e) => handleInputChange('newPassword', e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  isDarkMode 
                    ? 'bg-gray-700 border-gray-600 text-gray-100 placeholder-gray-400' 
                    : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                } ${errors.newPassword ? 'border-red-500' : ''}`}
                placeholder="Enter your new password"
              />
              {errors.newPassword && (
                <p className="mt-1 text-sm text-red-600">{errors.newPassword}</p>
              )}
            </div>

            {/* Confirm New Password */}
            <div>
              <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                Confirm New Password
              </label>
              <input
                type="password"
                value={formData.confirmPassword}
                onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  isDarkMode 
                    ? 'bg-gray-700 border-gray-600 text-gray-100 placeholder-gray-400' 
                    : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                } ${errors.confirmPassword ? 'border-red-500' : ''}`}
                placeholder="Confirm your new password"
              />
              {errors.confirmPassword && (
                <p className="mt-1 text-sm text-red-600">{errors.confirmPassword}</p>
              )}
            </div>

            {/* Buttons */}
            <div className="flex space-x-3 pt-4">
              <button
                type="button"
                onClick={handleClose}
                className={`flex-1 px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
                  isDarkMode 
                    ? 'bg-gray-600 hover:bg-gray-700 text-gray-100' 
                    : 'bg-gray-200 hover:bg-gray-300 text-gray-900'
                }`}
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={loading}
                className={`flex-1 px-4 py-2 text-sm font-medium text-white rounded-lg transition-colors ${
                  loading 
                    ? 'bg-blue-400 cursor-not-allowed' 
                    : 'bg-blue-600 hover:bg-blue-700'
                }`}
              >
                {loading ? 'Changing...' : 'Change Password'}
              </button>
            </div>
          </form>

          {/* API Key Management Section */}
          <div className="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
            <h3 className={`text-md font-medium mb-4 ${isDarkMode ? 'text-gray-100' : 'text-gray-900'}`}>
              Alpaca API Keys
            </h3>

            {/* API Key Success Message */}
            {apiKeySuccessMessage && (
              <div className="mb-4 p-3 bg-green-100 border border-green-400 text-green-700 rounded-lg text-sm">
                {apiKeySuccessMessage}
              </div>
            )}

            {/* API Key General Error */}
            {apiKeyErrors.general && (
              <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-lg text-sm">
                {apiKeyErrors.general}
              </div>
            )}

            {/* Existing API Key Display */}
            {apiKeyMetadata && !showApiKeyForm && (
              <div className={`p-4 rounded-lg mb-4 ${isDarkMode ? 'bg-gray-700' : 'bg-gray-50'}`}>
                <div className="flex items-center justify-between">
                  <div>
                    <div className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                      API Key: **** **** **** {apiKeyMetadata.last4}
                    </div>
                    <div className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                      Added: {new Date(apiKeyMetadata.created_at).toLocaleDateString()}
                    </div>
                  </div>
                  <div className="flex space-x-2">
                    <button
                      type="button"
                      onClick={() => setShowApiKeyForm(true)}
                      disabled={apiKeyLoading}
                      className="px-3 py-1 text-sm bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded transition-colors"
                    >
                      Replace
                    </button>
                    <button
                      type="button"
                      onClick={handleDeleteApiKey}
                      disabled={apiKeyLoading}
                      className="px-3 py-1 text-sm bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white rounded transition-colors"
                    >
                      {apiKeyLoading ? 'Deleting...' : 'Delete'}
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Add API Key Button */}
            {!apiKeyMetadata && !showApiKeyForm && (
              <button
                type="button"
                onClick={() => setShowApiKeyForm(true)}
                className="w-full px-4 py-2 text-sm font-medium bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
              >
                Add Alpaca API Key
              </button>
            )}

            {/* API Key Form */}
            {showApiKeyForm && (
              <form onSubmit={handleApiKeySubmit} className="space-y-4">
                <div>
                  <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    API Key
                  </label>
                  <input
                    type="text"
                    value={apiKeyFormData.apiKey}
                    onChange={(e) => handleApiKeyInputChange('apiKey', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      isDarkMode
                        ? 'bg-gray-700 border-gray-600 text-gray-100 placeholder-gray-400'
                        : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                    } ${apiKeyErrors.apiKey ? 'border-red-500' : ''}`}
                    placeholder="Enter your Alpaca API Key"
                  />
                  {apiKeyErrors.apiKey && (
                    <p className="mt-1 text-sm text-red-600">{apiKeyErrors.apiKey}</p>
                  )}
                </div>

                <div>
                  <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                    API Secret
                  </label>
                  <input
                    type="password"
                    value={apiKeyFormData.apiSecret}
                    onChange={(e) => handleApiKeyInputChange('apiSecret', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                      isDarkMode
                        ? 'bg-gray-700 border-gray-600 text-gray-100 placeholder-gray-400'
                        : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                    } ${apiKeyErrors.apiSecret ? 'border-red-500' : ''}`}
                    placeholder="Enter your Alpaca API Secret"
                  />
                  {apiKeyErrors.apiSecret && (
                    <p className="mt-1 text-sm text-red-600">{apiKeyErrors.apiSecret}</p>
                  )}
                </div>

                <div className="flex space-x-3 pt-2">
                  <button
                    type="button"
                    onClick={() => {
                      setShowApiKeyForm(false);
                      setApiKeyFormData({ apiKey: '', apiSecret: '' });
                      setApiKeyErrors({});
                    }}
                    disabled={apiKeyLoading}
                    className={`flex-1 px-4 py-2 text-sm font-medium rounded-lg transition-colors ${
                      isDarkMode
                        ? 'bg-gray-600 hover:bg-gray-700 text-gray-100'
                        : 'bg-gray-200 hover:bg-gray-300 text-gray-900'
                    } ${apiKeyLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                  >
                    Cancel
                  </button>
                  <button
                    type="submit"
                    disabled={apiKeyLoading}
                    className={`flex-1 px-4 py-2 text-sm font-medium text-white rounded-lg transition-colors ${
                      apiKeyLoading
                        ? 'bg-blue-400 cursor-not-allowed'
                        : 'bg-blue-600 hover:bg-blue-700'
                    }`}
                  >
                    {apiKeyLoading ? 'Saving...' : (apiKeyMetadata ? 'Replace Key' : 'Save Key')}
                  </button>
                </div>
              </form>
            )}

            {/* Security Notice */}
            <div className={`mt-4 p-3 rounded-lg text-xs ${isDarkMode ? 'bg-gray-700 text-gray-300' : 'bg-gray-50 text-gray-600'}`}>
              <div className="flex items-start space-x-2">
                <svg className="w-4 h-4 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
                <div>
                  <strong>Security:</strong> Your API secret is encrypted using military-grade AES-256-GCM encryption before storage.
                  It is never stored in plaintext and cannot be viewed after saving.
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
