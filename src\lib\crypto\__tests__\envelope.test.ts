/**
 * @jest-environment jsdom
 */

import {
  generateDek,
  encryptWithDek,
  decryptWithDek,
  wrapDek,
  unwrapDek,
  envelopeEncrypt,
  envelopeDecrypt,
  generateMasterKey,
  CryptoError,
} from '../envelope';

// Mock crypto.subtle for testing environment
const mockCrypto = {
  subtle: {
    generateKey: jest.fn(),
    encrypt: jest.fn(),
    decrypt: jest.fn(),
    importKey: jest.fn(),
    exportKey: jest.fn(),
  },
  getRandomValues: jest.fn(),
};

// Setup crypto mock
Object.defineProperty(global, 'crypto', {
  value: mockCrypto,
  writable: true,
});

// Mock TextEncoder/TextDecoder
global.TextEncoder = class {
  encode(str: string) {
    return new Uint8Array(Buffer.from(str, 'utf8'));
  }
};

global.TextDecoder = class {
  decode(buffer: ArrayBuffer) {
    return Buffer.from(buffer).toString('utf8');
  }
};

// Mock btoa/atob
global.btoa = (str: string) => Buffer.from(str, 'binary').toString('base64');
global.atob = (str: string) => Buffer.from(str, 'base64').toString('binary');

describe('Envelope Encryption', () => {
  const testSecret = 'test-api-secret-12345';
  const testMasterKey = 'dGVzdC1tYXN0ZXIta2V5LTEyMzQ1Njc4OTBhYmNkZWY='; // base64 encoded

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock crypto.getRandomValues
    mockCrypto.getRandomValues.mockImplementation((array: Uint8Array) => {
      for (let i = 0; i < array.length; i++) {
        array[i] = Math.floor(Math.random() * 256);
      }
      return array;
    });
  });

  describe('generateDek', () => {
    it('should generate a 256-bit AES-GCM key', async () => {
      const mockKey = { type: 'secret', algorithm: { name: 'AES-GCM', length: 256 } };
      mockCrypto.subtle.generateKey.mockResolvedValue(mockKey);

      const dek = await generateDek();

      expect(mockCrypto.subtle.generateKey).toHaveBeenCalledWith(
        { name: 'AES-GCM', length: 256 },
        true,
        ['encrypt', 'decrypt']
      );
      expect(dek).toBe(mockKey);
    });

    it('should throw CryptoError on failure', async () => {
      mockCrypto.subtle.generateKey.mockRejectedValue(new Error('Generation failed'));

      await expect(generateDek()).rejects.toThrow(CryptoError);
      await expect(generateDek()).rejects.toThrow('Failed to generate DEK');
    });
  });

  describe('encryptWithDek', () => {
    it('should encrypt data with AES-GCM', async () => {
      const mockKey = { type: 'secret' };
      const mockEncryptedData = new Uint8Array([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20]);
      
      mockCrypto.subtle.encrypt.mockResolvedValue(mockEncryptedData.buffer);

      const result = await encryptWithDek(testSecret, mockKey as CryptoKey);

      expect(mockCrypto.subtle.encrypt).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'AES-GCM',
          tagLength: 128,
        }),
        mockKey,
        expect.any(Uint8Array)
      );

      expect(result).toHaveProperty('encryptedData');
      expect(result).toHaveProperty('iv');
      expect(result).toHaveProperty('tag');
      expect(typeof result.encryptedData).toBe('string');
      expect(typeof result.iv).toBe('string');
      expect(typeof result.tag).toBe('string');
    });

    it('should throw CryptoError on encryption failure', async () => {
      const mockKey = { type: 'secret' };
      mockCrypto.subtle.encrypt.mockRejectedValue(new Error('Encryption failed'));

      await expect(encryptWithDek(testSecret, mockKey as CryptoKey)).rejects.toThrow(CryptoError);
    });
  });

  describe('decryptWithDek', () => {
    it('should decrypt data with AES-GCM', async () => {
      const mockKey = { type: 'secret' };
      const mockDecryptedData = new TextEncoder().encode(testSecret);
      
      mockCrypto.subtle.decrypt.mockResolvedValue(mockDecryptedData.buffer);

      const input = {
        encryptedData: btoa('encrypted'),
        iv: btoa('iv12bytes'),
        tag: btoa('tag16bytes'),
      };

      const result = await decryptWithDek(input, mockKey as CryptoKey);

      expect(mockCrypto.subtle.decrypt).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'AES-GCM',
          tagLength: 128,
        }),
        mockKey,
        expect.any(Uint8Array)
      );

      expect(result).toBe(testSecret);
    });

    it('should throw CryptoError on decryption failure', async () => {
      const mockKey = { type: 'secret' };
      mockCrypto.subtle.decrypt.mockRejectedValue(new Error('Decryption failed'));

      const input = {
        encryptedData: btoa('encrypted'),
        iv: btoa('iv12bytes'),
        tag: btoa('tag16bytes'),
      };

      await expect(decryptWithDek(input, mockKey as CryptoKey)).rejects.toThrow(CryptoError);
    });
  });

  describe('wrapDek', () => {
    it('should wrap DEK with master key', async () => {
      const mockDek = { type: 'secret' };
      const mockKek = { type: 'secret' };
      const mockDekBytes = new Uint8Array(32);
      const mockWrappedDek = new Uint8Array(48); // 32 bytes + 16 byte tag

      mockCrypto.subtle.importKey.mockResolvedValue(mockKek);
      mockCrypto.subtle.exportKey.mockResolvedValue(mockDekBytes.buffer);
      mockCrypto.subtle.encrypt.mockResolvedValue(mockWrappedDek.buffer);

      const result = await wrapDek(mockDek as CryptoKey, testMasterKey);

      expect(mockCrypto.subtle.importKey).toHaveBeenCalledWith(
        'raw',
        expect.any(ArrayBuffer),
        { name: 'AES-GCM' },
        false,
        ['encrypt']
      );

      expect(mockCrypto.subtle.exportKey).toHaveBeenCalledWith('raw', mockDek);
      expect(typeof result).toBe('string');
    });

    it('should throw CryptoError on wrapping failure', async () => {
      const mockDek = { type: 'secret' };
      mockCrypto.subtle.importKey.mockRejectedValue(new Error('Import failed'));

      await expect(wrapDek(mockDek as CryptoKey, testMasterKey)).rejects.toThrow(CryptoError);
    });
  });

  describe('unwrapDek', () => {
    it('should unwrap DEK with master key', async () => {
      const mockKek = { type: 'secret' };
      const mockDek = { type: 'secret' };
      const mockDekBytes = new Uint8Array(32);
      const wrappedDek = btoa('iv12bytes' + 'wrappedDekData');

      mockCrypto.subtle.importKey.mockResolvedValueOnce(mockKek).mockResolvedValueOnce(mockDek);
      mockCrypto.subtle.decrypt.mockResolvedValue(mockDekBytes.buffer);

      const result = await unwrapDek(wrappedDek, testMasterKey);

      expect(mockCrypto.subtle.importKey).toHaveBeenCalledTimes(2);
      expect(mockCrypto.subtle.decrypt).toHaveBeenCalled();
      expect(result).toBe(mockDek);
    });

    it('should throw CryptoError on unwrapping failure', async () => {
      const wrappedDek = btoa('invalid');
      mockCrypto.subtle.importKey.mockRejectedValue(new Error('Import failed'));

      await expect(unwrapDek(wrappedDek, testMasterKey)).rejects.toThrow(CryptoError);
    });
  });

  describe('envelopeEncrypt', () => {
    it('should perform complete envelope encryption', async () => {
      const mockDek = { type: 'secret' };
      const mockEncryptedData = new Uint8Array(20);
      const mockKek = { type: 'secret' };
      const mockDekBytes = new Uint8Array(32);
      const mockWrappedDek = new Uint8Array(48);

      mockCrypto.subtle.generateKey.mockResolvedValue(mockDek);
      mockCrypto.subtle.encrypt.mockResolvedValueOnce(mockEncryptedData.buffer).mockResolvedValueOnce(mockWrappedDek.buffer);
      mockCrypto.subtle.importKey.mockResolvedValue(mockKek);
      mockCrypto.subtle.exportKey.mockResolvedValue(mockDekBytes.buffer);

      const result = await envelopeEncrypt(testSecret, testMasterKey);

      expect(result).toHaveProperty('encryptedData');
      expect(result).toHaveProperty('iv');
      expect(result).toHaveProperty('tag');
      expect(result).toHaveProperty('encryptedDek');
      expect(typeof result.encryptedDek).toBe('string');
    });

    it('should throw CryptoError on envelope encryption failure', async () => {
      mockCrypto.subtle.generateKey.mockRejectedValue(new Error('Generation failed'));

      await expect(envelopeEncrypt(testSecret, testMasterKey)).rejects.toThrow(CryptoError);
    });
  });

  describe('envelopeDecrypt', () => {
    it('should perform complete envelope decryption', async () => {
      const mockKek = { type: 'secret' };
      const mockDek = { type: 'secret' };
      const mockDekBytes = new Uint8Array(32);
      const mockDecryptedData = new TextEncoder().encode(testSecret);

      const input = {
        encryptedData: btoa('encrypted'),
        iv: btoa('iv12bytes'),
        tag: btoa('tag16bytes'),
        encryptedDek: btoa('wrappedDek'),
      };

      mockCrypto.subtle.importKey.mockResolvedValueOnce(mockKek).mockResolvedValueOnce(mockDek);
      mockCrypto.subtle.decrypt.mockResolvedValueOnce(mockDekBytes.buffer).mockResolvedValueOnce(mockDecryptedData.buffer);

      const result = await envelopeDecrypt(input, testMasterKey);

      expect(result).toBe(testSecret);
    });

    it('should throw CryptoError on envelope decryption failure', async () => {
      const input = {
        encryptedData: btoa('encrypted'),
        iv: btoa('iv12bytes'),
        tag: btoa('tag16bytes'),
        encryptedDek: btoa('wrappedDek'),
      };

      mockCrypto.subtle.importKey.mockRejectedValue(new Error('Import failed'));

      await expect(envelopeDecrypt(input, testMasterKey)).rejects.toThrow(CryptoError);
    });
  });

  describe('generateMasterKey', () => {
    it('should generate a base64 encoded 256-bit key', () => {
      const key = generateMasterKey();
      
      expect(typeof key).toBe('string');
      expect(key.length).toBeGreaterThan(40); // Base64 encoded 32 bytes should be ~44 chars
      
      // Should be valid base64
      expect(() => atob(key)).not.toThrow();
      
      // Decoded should be 32 bytes
      const decoded = atob(key);
      expect(decoded.length).toBe(32);
    });
  });

  describe('Roundtrip Tests', () => {
    it('should successfully encrypt and decrypt data', async () => {
      // Mock successful crypto operations for roundtrip
      const mockDek = { type: 'secret' };
      const mockKek = { type: 'secret' };
      const mockDekBytes = new Uint8Array(32);
      const mockEncryptedData = new Uint8Array(20);
      const mockWrappedDek = new Uint8Array(48);
      const mockDecryptedData = new TextEncoder().encode(testSecret);

      // Setup mocks for encryption
      mockCrypto.subtle.generateKey.mockResolvedValue(mockDek);
      mockCrypto.subtle.encrypt.mockResolvedValueOnce(mockEncryptedData.buffer).mockResolvedValueOnce(mockWrappedDek.buffer);
      mockCrypto.subtle.importKey.mockResolvedValue(mockKek);
      mockCrypto.subtle.exportKey.mockResolvedValue(mockDekBytes.buffer);

      const encrypted = await envelopeEncrypt(testSecret, testMasterKey);

      // Setup mocks for decryption
      mockCrypto.subtle.importKey.mockResolvedValueOnce(mockKek).mockResolvedValueOnce(mockDek);
      mockCrypto.subtle.decrypt.mockResolvedValueOnce(mockDekBytes.buffer).mockResolvedValueOnce(mockDecryptedData.buffer);

      const decrypted = await envelopeDecrypt(encrypted, testMasterKey);

      expect(decrypted).toBe(testSecret);
    });

    it('should fail with wrong master key', async () => {
      const wrongMasterKey = 'wrong-key-base64-encoded-here';
      
      const mockDek = { type: 'secret' };
      const mockKek = { type: 'secret' };
      const mockDekBytes = new Uint8Array(32);
      const mockEncryptedData = new Uint8Array(20);
      const mockWrappedDek = new Uint8Array(48);

      // Setup mocks for encryption
      mockCrypto.subtle.generateKey.mockResolvedValue(mockDek);
      mockCrypto.subtle.encrypt.mockResolvedValueOnce(mockEncryptedData.buffer).mockResolvedValueOnce(mockWrappedDek.buffer);
      mockCrypto.subtle.importKey.mockResolvedValue(mockKek);
      mockCrypto.subtle.exportKey.mockResolvedValue(mockDekBytes.buffer);

      const encrypted = await envelopeEncrypt(testSecret, testMasterKey);

      // Setup mocks for failed decryption with wrong key
      mockCrypto.subtle.importKey.mockResolvedValue(mockKek);
      mockCrypto.subtle.decrypt.mockRejectedValue(new Error('Decryption failed'));

      await expect(envelopeDecrypt(encrypted, wrongMasterKey)).rejects.toThrow(CryptoError);
    });
  });
});
