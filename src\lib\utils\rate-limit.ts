/**
 * Simple in-memory rate limiter for API routes
 * In production, consider using Redis or a more robust solution
 */

interface RateLimitConfig {
  interval: number; // Time window in milliseconds
  uniqueTokenPerInterval: number; // Max unique tokens per interval
}

interface RateLimitResult {
  success: boolean;
  limit: number;
  remaining: number;
  reset: number;
}

class RateLimiter {
  private requests: Map<string, number[]> = new Map();
  private config: RateLimitConfig;

  constructor(config: RateLimitConfig) {
    this.config = config;
  }

  async check(limit: number, token: string): Promise<RateLimitResult> {
    const now = Date.now();
    const windowStart = now - this.config.interval;

    // Get existing requests for this token
    const tokenRequests = this.requests.get(token) || [];
    
    // Filter out requests outside the current window
    const validRequests = tokenRequests.filter(time => time > windowStart);
    
    // Check if limit exceeded
    const success = validRequests.length < limit;
    
    if (success) {
      // Add current request
      validRequests.push(now);
      this.requests.set(token, validRequests);
    }

    // Clean up old entries periodically
    if (Math.random() < 0.01) { // 1% chance to clean up
      this.cleanup();
    }

    return {
      success,
      limit,
      remaining: Math.max(0, limit - validRequests.length - (success ? 0 : 1)),
      reset: windowStart + this.config.interval,
    };
  }

  private cleanup() {
    const now = Date.now();
    const windowStart = now - this.config.interval;

    for (const [token, requests] of this.requests.entries()) {
      const validRequests = requests.filter(time => time > windowStart);
      if (validRequests.length === 0) {
        this.requests.delete(token);
      } else {
        this.requests.set(token, validRequests);
      }
    }
  }
}

// Factory function to create rate limiters
export function rateLimit(config: RateLimitConfig): RateLimiter {
  return new RateLimiter(config);
}
