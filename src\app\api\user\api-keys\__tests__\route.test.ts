/**
 * @jest-environment node
 */

import { NextRequest } from 'next/server';
import { POST, GET, DELETE } from '../route';

// Mock dependencies
jest.mock('@supabase/supabase-js');
jest.mock('@/lib/crypto/envelope');
jest.mock('@/lib/utils/rate-limit');

const mockSupabaseServer = {
  from: jest.fn(),
  auth: {
    getUser: jest.fn(),
  },
};

const mockEnvelopeEncrypt = jest.fn();
const mockEnvelopeDecrypt = jest.fn();
const mockRateLimit = jest.fn();

// Mock modules
jest.mock('@supabase/supabase-js', () => ({
  createClient: jest.fn(() => mockSupabaseServer),
}));

jest.mock('@/lib/crypto/envelope', () => ({
  envelopeEncrypt: mockEnvelopeEncrypt,
  envelopeDecrypt: mockEnvelopeDecrypt,
}));

jest.mock('@/lib/utils/rate-limit', () => ({
  rateLimit: jest.fn(() => ({
    check: mockRateLimit,
  })),
}));

// Mock environment variables
const originalEnv = process.env;
beforeAll(() => {
  process.env = {
    ...originalEnv,
    NEXT_PUBLIC_SUPABASE_URL: 'https://test.supabase.co',
    SUPABASE_SERVICE_ROLE_KEY: 'test-service-key',
    MASTER_KEY: 'dGVzdC1tYXN0ZXIta2V5LTEyMzQ1Njc4OTBhYmNkZWY=',
  };
});

afterAll(() => {
  process.env = originalEnv;
});

describe('/api/user/api-keys', () => {
  const validApiKey = 'ABCDEFGHIJ1234567890';
  const validApiSecret = 'abcdefghijklmnopqrstuvwxyz1234567890ABCD';
  const mockUserId = 'user-123';
  const mockToken = 'valid-jwt-token';

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Default rate limit success
    mockRateLimit.mockResolvedValue({ success: true });
    
    // Default auth success
    mockSupabaseServer.auth.getUser.mockResolvedValue({
      data: { user: { id: mockUserId } },
      error: null,
    });
  });

  describe('POST /api/user/api-keys', () => {
    it('should save API key successfully', async () => {
      const mockEncryptionResult = {
        encryptedData: 'encrypted-secret',
        iv: 'test-iv',
        tag: 'test-tag',
        encryptedDek: 'encrypted-dek',
      };

      const mockDbResponse = {
        data: {
          provider: 'alpaca',
          api_key_last4: '7890',
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
        },
        error: null,
      };

      mockEnvelopeEncrypt.mockResolvedValue(mockEncryptionResult);
      mockSupabaseServer.from.mockReturnValue({
        upsert: jest.fn().mockReturnValue({
          select: jest.fn().mockReturnValue({
            single: jest.fn().mockResolvedValue(mockDbResponse),
          }),
        }),
      });

      const request = new NextRequest('http://localhost:3000/api/user/api-keys', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${mockToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          provider: 'alpaca',
          apiKey: validApiKey,
          apiSecret: validApiSecret,
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toEqual({
        provider: 'alpaca',
        last4: '7890',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      });

      // Verify encryption was called with correct parameters
      expect(mockEnvelopeEncrypt).toHaveBeenCalledWith(
        validApiSecret,
        process.env.MASTER_KEY
      );

      // Verify database upsert was called
      expect(mockSupabaseServer.from).toHaveBeenCalledWith('user_api_secrets');
    });

    it('should reject invalid API key format', async () => {
      const request = new NextRequest('http://localhost:3000/api/user/api-keys', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${mockToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          provider: 'alpaca',
          apiKey: 'invalid-key',
          apiSecret: validApiSecret,
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Invalid API key format');
      expect(mockEnvelopeEncrypt).not.toHaveBeenCalled();
    });

    it('should reject invalid API secret format', async () => {
      const request = new NextRequest('http://localhost:3000/api/user/api-keys', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${mockToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          provider: 'alpaca',
          apiKey: validApiKey,
          apiSecret: 'invalid-secret',
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Invalid API secret format');
      expect(mockEnvelopeEncrypt).not.toHaveBeenCalled();
    });

    it('should reject unauthorized requests', async () => {
      mockSupabaseServer.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: { message: 'Invalid token' },
      });

      const request = new NextRequest('http://localhost:3000/api/user/api-keys', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer invalid-token`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          provider: 'alpaca',
          apiKey: validApiKey,
          apiSecret: validApiSecret,
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe('Unauthorized');
      expect(mockEnvelopeEncrypt).not.toHaveBeenCalled();
    });

    it('should handle rate limiting', async () => {
      mockRateLimit.mockResolvedValue({ success: false });

      const request = new NextRequest('http://localhost:3000/api/user/api-keys', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${mockToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          provider: 'alpaca',
          apiKey: validApiKey,
          apiSecret: validApiSecret,
        }),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(429);
      expect(data.error).toBe('Too many requests. Please try again later.');
    });
  });

  describe('GET /api/user/api-keys', () => {
    it('should return masked API key metadata', async () => {
      const mockDbResponse = {
        data: {
          provider: 'alpaca',
          api_key_last4: '7890',
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
        },
        error: null,
      };

      mockSupabaseServer.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue(mockDbResponse),
            }),
          }),
        }),
      });

      const request = new NextRequest('http://localhost:3000/api/user/api-keys?provider=alpaca', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${mockToken}`,
        },
      });

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toEqual({
        provider: 'alpaca',
        last4: '7890',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      });

      // Verify no encrypted data is returned
      expect(data).not.toHaveProperty('encrypted_key');
      expect(data).not.toHaveProperty('iv');
      expect(data).not.toHaveProperty('tag');
      expect(data).not.toHaveProperty('encrypted_dek');
    });

    it('should return null when no API key exists', async () => {
      mockSupabaseServer.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue({
                data: null,
                error: { code: 'PGRST116' }, // No rows returned
              }),
            }),
          }),
        }),
      });

      const request = new NextRequest('http://localhost:3000/api/user/api-keys?provider=alpaca', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${mockToken}`,
        },
      });

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toBeNull();
    });

    it('should reject unauthorized requests', async () => {
      mockSupabaseServer.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: { message: 'Invalid token' },
      });

      const request = new NextRequest('http://localhost:3000/api/user/api-keys?provider=alpaca', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer invalid-token`,
        },
      });

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe('Unauthorized');
    });
  });

  describe('DELETE /api/user/api-keys', () => {
    it('should delete API key successfully', async () => {
      mockSupabaseServer.from.mockReturnValue({
        delete: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockResolvedValue({
              error: null,
            }),
          }),
        }),
      });

      const request = new NextRequest('http://localhost:3000/api/user/api-keys?provider=alpaca', {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${mockToken}`,
        },
      });

      const response = await DELETE(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data).toEqual({ success: true });
    });

    it('should reject unauthorized requests', async () => {
      mockSupabaseServer.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: { message: 'Invalid token' },
      });

      const request = new NextRequest('http://localhost:3000/api/user/api-keys?provider=alpaca', {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer invalid-token`,
        },
      });

      const response = await DELETE(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.error).toBe('Unauthorized');
    });

    it('should handle rate limiting', async () => {
      mockRateLimit.mockResolvedValue({ success: false });

      const request = new NextRequest('http://localhost:3000/api/user/api-keys?provider=alpaca', {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${mockToken}`,
        },
      });

      const response = await DELETE(request);
      const data = await response.json();

      expect(response.status).toBe(429);
      expect(data.error).toBe('Too many requests. Please try again later.');
    });
  });

  describe('Security Tests', () => {
    it('should never return plaintext secrets', async () => {
      // Test that even if database somehow contains plaintext, it's not returned
      const mockDbResponse = {
        data: {
          provider: 'alpaca',
          api_key_last4: '7890',
          created_at: '2024-01-01T00:00:00Z',
          updated_at: '2024-01-01T00:00:00Z',
          // These should never be returned even if present
          encrypted_key: 'encrypted-data',
          iv: 'test-iv',
          tag: 'test-tag',
          encrypted_dek: 'encrypted-dek',
          plaintext_secret: 'this-should-never-exist',
        },
        error: null,
      };

      mockSupabaseServer.from.mockReturnValue({
        select: jest.fn().mockReturnValue({
          eq: jest.fn().mockReturnValue({
            eq: jest.fn().mockReturnValue({
              single: jest.fn().mockResolvedValue(mockDbResponse),
            }),
          }),
        }),
      });

      const request = new NextRequest('http://localhost:3000/api/user/api-keys?provider=alpaca', {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${mockToken}`,
        },
      });

      const response = await GET(request);
      const data = await response.json();

      // Verify only safe metadata is returned
      expect(data).toEqual({
        provider: 'alpaca',
        last4: '7890',
        created_at: '2024-01-01T00:00:00Z',
        updated_at: '2024-01-01T00:00:00Z',
      });

      // Verify sensitive data is not returned
      expect(data).not.toHaveProperty('encrypted_key');
      expect(data).not.toHaveProperty('iv');
      expect(data).not.toHaveProperty('tag');
      expect(data).not.toHaveProperty('encrypted_dek');
      expect(data).not.toHaveProperty('plaintext_secret');
    });

    it('should validate input to prevent injection attacks', async () => {
      const maliciousInput = {
        provider: 'alpaca"; DROP TABLE user_api_secrets; --',
        apiKey: validApiKey,
        apiSecret: validApiSecret,
      };

      const request = new NextRequest('http://localhost:3000/api/user/api-keys', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${mockToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(maliciousInput),
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Unsupported provider. Only "alpaca" is supported.');
    });
  });
});
