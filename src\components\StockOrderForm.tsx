interface StockOrderFormProps {
  isDarkMode: boolean;
}

export default function StockOrderForm({ isDarkMode }: StockOrderFormProps) {
  return (
    <div className={`h-full ${isDarkMode ? 'bg-gray-900' : 'bg-white'}`}>
      <h2 className={`text-xl font-semibold mb-6 ${isDarkMode ? 'text-gray-100' : 'text-gray-900'}`}>Stock Order Entry</h2>
      
      <form className="space-y-4">
        {/* Symbol Input */}
        <div>
          <label htmlFor="symbol" className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-100' : 'text-gray-900'}`}>
            Symbol
          </label>
          <input
            type="text"
            id="symbol"
            name="symbol"
            placeholder="e.g., AAPL"
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              isDarkMode 
                ? 'border-gray-600 bg-gray-800 text-gray-100' 
                : 'border-gray-300 bg-white text-gray-900'
            }`}
          />
        </div>

        {/* Order Type */}
        <div>
          <label htmlFor="orderType" className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-100' : 'text-gray-900'}`}>
            Order Type
          </label>
          <select
            id="orderType"
            name="orderType"
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              isDarkMode 
                ? 'border-gray-600 bg-gray-800 text-gray-100' 
                : 'border-gray-300 bg-white text-gray-900'
            }`}
          >
            <option value="market">Market</option>
            <option value="limit">Limit</option>
            <option value="stop">Stop</option>
            <option value="stopLimit">Stop Limit</option>
          </select>
        </div>

        {/* Side */}
        <div>
          <label className={`block text-sm font-medium mb-2 ${isDarkMode ? 'text-gray-100' : 'text-gray-900'}`}>
            Side
          </label>
          <div className="flex space-x-4">
            <label className="flex items-center">
              <input
                type="radio"
                name="side"
                value="buy"
                defaultChecked
                className="mr-2 text-green-600 focus:ring-green-500"
              />
              <span className="text-green-600 font-medium">Buy</span>
            </label>
            <label className="flex items-center">
              <input
                type="radio"
                name="side"
                value="sell"
                className="mr-2 text-red-600 focus:ring-red-500"
              />
              <span className="text-red-600 font-medium">Sell</span>
            </label>
          </div>
        </div>

        {/* Quantity */}
        <div>
          <label htmlFor="quantity" className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-100' : 'text-gray-900'}`}>
            Quantity
          </label>
          <input
            type="number"
            id="quantity"
            name="quantity"
            min="1"
            placeholder="100"
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              isDarkMode 
                ? 'border-gray-600 bg-gray-800 text-gray-100' 
                : 'border-gray-300 bg-white text-gray-900'
            }`}
          />
        </div>

        {/* Price */}
        <div>
          <label htmlFor="price" className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-100' : 'text-gray-900'}`}>
            Price
          </label>
          <input
            type="number"
            id="price"
            name="price"
            step="0.01"
            min="0.01"
            placeholder="150.00"
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              isDarkMode 
                ? 'border-gray-600 bg-gray-800 text-gray-100' 
                : 'border-gray-300 bg-white text-gray-900'
            }`}
          />
        </div>

        {/* Time in Force */}
        <div>
          <label htmlFor="timeInForce" className={`block text-sm font-medium mb-1 ${isDarkMode ? 'text-gray-100' : 'text-gray-900'}`}>
            Time in Force
          </label>
          <select
            id="timeInForce"
            name="timeInForce"
            className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
              isDarkMode 
                ? 'border-gray-600 bg-gray-800 text-gray-100' 
                : 'border-gray-300 bg-white text-gray-900'
            }`}
          >
            <option value="day">Day</option>
            <option value="gtc">Good Till Canceled</option>
            <option value="ioc">Immediate or Cancel</option>
            <option value="fok">Fill or Kill</option>
          </select>
        </div>

        {/* Submit Button */}
        <button
          type="submit"
          className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          Place Order
        </button>

        {/* Cancel Button */}
        <button
          type="button"
          className="w-full bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
        >
          Cancel
        </button>
      </form>
    </div>
  );
}
